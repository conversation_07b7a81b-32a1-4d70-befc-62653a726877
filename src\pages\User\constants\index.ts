/**
 * Constants and configuration for User pages
 */

// Thai localization constants
export const THAI_MONTHS = [
  'มกราคม',
  'กุมภาพันธ์',
  'มีนาคม',
  'เมษายน',
  'พฤษภาคม',
  'มิถุนายน',
  'กรกฎาคม',
  'สิงหาคม',
  'กันยายน',
  'ตุลาคม',
  'พฤศจิกายน',
  'ธันวาคม',
] as const

export const THAI_DAYS_SHORT = [
  'อาทิตย์',
  'จันทร์',
  'อังคาร',
  'พุธ',
  'พฤหัสบดี',
  'ศุกร์',
  'เสาร์',
] as const

export const DAY_LETTERS = ['S', 'M', 'T', 'W', 'T', 'F', 'S'] as const

// Map configuration
export const MAP_CONFIG = {
  DEFAULT_LOCATION: {
    lat: 13.281294489182047,
    lng: 100.9240488495316,
  },
  DEFAULT_ZOOM: 16,
  LOCATION_OFFSET_RANGE: 0.001,
  TILE_LAYER_URL: 'https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png',
  ATTRIBUTION: '&copy; OpenStreetMap contributors',
} as const

// Time and date formats
export const DATE_FORMATS = {
  TIME: 'HH:mm',
  DATE_DISPLAY: 'ddd D MMM YYYY',
  API_DATE: 'YYYY-MM-DD',
} as const

// Refresh intervals (in milliseconds)
export const REFRESH_INTERVALS = {
  TIME_UPDATE: 1000, // 1 second
  ATTENDANCE_REFRESH: 30000, // 30 seconds
} as const

// Chart configuration
export const CHART_CONFIG = {
  COLORS: {
    PRIMARY: '#26A69A',
    SECONDARY: '#AB47BC',
    SUCCESS: '#4CAF50',
    WARNING: '#FF9800',
    ERROR: '#F44336',
    TEAL: '#26A69A',
    PURPLE: '#AB47BC',
  },
  PADDING_MULTIPLIER: 1.2,
  DEFAULT_MAX_HOURS: 50,
  DEFAULT_MAX_LATE_DAYS: 8,
} as const

// Filter options for user management
export const USER_FILTER_OPTIONS = [
  { label: 'รหัสพนักงาน', value: 'user_id' },
  { label: 'ชื่อ-นามสกุล', value: 'fullname' },
] as const

// Default values
export const DEFAULT_VALUES = {
  PHONE_NUMBER: '0989898776',
  EMPLOYEE_TYPE: 'ประจำ',
  LOCATION_NAME: 'สุขถาวรโอสถ',
  LOCATION_SERVICE: 'สาขาบางแสน',
  AVATAR_PLACEHOLDER: 'https://cdn.quasar.dev/img/avatar.png',
} as const

// Time format regex
export const TIME_FORMAT_REGEX = /^\d{1,2}:\d{2}(:\d{2})?$/

// Loading states
export const LOADING_STATES = {
  WORK_STATS: 'workStats',
  EMPLOYEE_DATA: 'employeeData',
  WORK_HOURS: 'workHours',
  LATE_CHECK_IN: 'lateCheckIn',
} as const
