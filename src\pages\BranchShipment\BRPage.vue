<template>
  <BranchShipmentNavigation></BranchShipmentNavigation>
  <div class="container q-pa-md q-ma-md page-fade-in">
    <div class="row wrap-container">
      <div class="col-7 search-container">
        <SearchComponent placeholder="ค้นหา" />
      </div>
      <div class="col-2 filter-button-container">
        <FilterComponent v-model="selectedFilter" :filterOptions="filterOptions" class="q-mb-md" />
      </div>
    </div>
    <!-- <q-card flat class="custom-table">
            <q-table class="body-table" :rows="store.sto" :columns="columns" row-key="id" :pagination="pagination"
                :rows-per-page-options="[]" style="height: 100%; max-height: 700px"> -->
    <!-- <template #body-cell-actions="props">
                    <q-td class="q-gutter-x-sm" style="min-width: 100px">
                        <q-btn icon="edit" padding="none" flat style="color: #e19f62"
                            @click="handleOpenEdit(props.row)" />
                        <q-btn icon="delete" padding="none" style="color: #b53638" flat
                            @click="handleOpenDeleteDialog(props.row)" />
                        <q-btn icon="info" padding="none" style="color: #294888" flat
                            @click="handleOpenInfo(props.row)" />
                    </q-td>
                </template> -->

    <!-- <template #body-cell-status="props">
                    <q-td class="q-gutter-x-sm" style="min-width: 100px; text-align: center">
                        <q-badge :label="props.row.status" :class="{
                            'green-card': props.row.status === 'เสร็จสมบูรณ์',
                            'blue-card': props.row.status === 'ดำเนินการ',
                            'yellow-card': props.row.status === 'เตรียมรายการ',
                            'red-card': props.row.status === 'ยกเลิก'
                        }" class="q-ml-none" style="
              height: 30px;
              width: 100px;
              display: flex;
              align-items: left;
              justify-content: flex-start;
              border-radius: 8px ;
              padding-left: 8px;">
                        </q-badge>
                    </q-td>

                </template>
            </q-table>
        </q-card> -->
    <!-- Status Filter Component -->
    <StatusFilterComponent :status-types="statusTypes" :columns="columns" :pagination="pagination"
      @row-click="handleRowClick" @status-change="handleStatusChange" />
  </div>
</template>
<script setup lang="ts">
import type { QTableColumn } from 'quasar'
import BranchShipmentNavigation from 'src/components/BranchShipmentNavigation.vue'
import StatusFilterComponent from 'src/components/StatusFilterComponent.vue'
import FilterComponent from 'src/components/filterComponent.vue'
import SearchComponent from 'src/components/searchComponent.vue'
import { useStockTransferOrderStore } from 'src/stores/orders/stocktransferorder'
import type { StockTransferOrder } from 'src/types/stockTransferOrder'
import { onMounted, ref, computed } from 'vue'
const store = useStockTransferOrderStore()
onMounted(async () => {
  await store.fetchAllSTO()
})
const columns = <QTableColumn[]>[
  {
    name: 'code',
    label: 'เลขที่',
    field: 'code',
    align: 'left' as const,
    sortable: true,
  },
  {
    name: 'supplier',
    label: 'วันที่',
    field: 'request_date',
    align: 'left' as const,
    sortable: true,
  },
  {
    name: 'source_branch',
    label: 'สาขาต้นทาง',
    field: (row) => (row.source_branch ? row.source_branch.name : ''),
    align: 'left' as const,
    sortable: true,
  },
  {
    name: 'destiantion_branch',
    label: 'สาขาปลายทาง',
    field: (row) => (row.destination_branch ? row.destination_branch.name : ''),
    align: 'left' as const,
    sortable: true,
  },
  {
    name: 'user',
    label: 'พนักงาน',
    field: (row) => (row.user ? row.user.name : ''),
    align: 'left' as const,
    sortable: true,
  },

  {
    name: 'actions',
    label: '',
    align: 'center' as const,
    field: 'actions',
    sortable: false,
  },
]
const pagination = ref({
  rowsPerPage: 12,
})
const selectedFilter = ref<string>('')

const filterOptions = [
  { label: 'รหัสสั่งซื้อสินค้า', value: 'code' },
  { label: 'ชื่อบริษัทจำหน่าย', value: 'name' },
  { label: 'รหัสบริษัทจำหน่าย', value: 'supplier_number' },
]

// Status types configuration for the StatusFilterComponent
const statusTypes = computed(() => [
  {
    key: 'prepare',
    label: 'เตรียมรายการ',
    tableTitle: 'เตรียมรายการ',
    data: store.sto, // Using the same data for now
    defaultSelected: true
  },
  {
    key: 'process',
    label: 'ดำเนินรายการ',
    tableTitle: 'กำลังดำเนินการ',
    data: store.sto, // Using the same data for now
    defaultSelected: true
  }
])

const handleStatusChange = (statuses: Record<string, boolean>) => {
  console.log('Status changed:', statuses)
  // You can add additional logic here if needed
}

const handleRowClick = async (event: Event, row: unknown) => {
  // Type-cast the row to StockTransferOrder since we know it's from our STO data
  console.log('🚀 Clicked Row Object:', row as StockTransferOrder)
  // Add your row click logic here
}
</script>
<style scoped>
.green-card {
  background-color: #439e62;
  /* สีเขียว */
  color: white;
}

.yellow-card {
  background-color: #ed9b53;
  /* สีเหลือง */
  color: white;
}

.red-card {
  background-color: #b53638;
  /* สีแดง */
  color: white;
}

.blue-card {
  /* สีแดง */
  background-color: #83a7d8;
  color: white;
}

:deep(.q-table thead tr) {
  background-color: #91d2c1;
}

.container {
  background-color: white;
  border-radius: 10px;
  height: 100%;
  max-height: 800px;
}

.add-button {
  background-color: #294888;
  color: white;
  border-radius: 5px;
  width: 100%;
  min-width: 141px;
  max-width: 300px;
  height: 40px;
}

.wrap-container {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
  align-items: stretch;
}

.search-container {
  flex: 1;
  min-width: 300px;
  max-width: 1000px;
}

.filter-button-container {
  display: flex;
  gap: 16px;
  align-items: center;
  justify-content: flex-start;
}

.body-table {
  background-color: #e1edea;
}

.custom-table {
  border-radius: 10px;
  overflow: hidden;
}

@media (max-width: 1200px) {
  .wrap-container {
    flex-direction: column;
    align-items: stretch;
  }

  .filter-button-container {
    flex-direction: row;
    width: 100%;
  }

  .text-right {
    text-align: left;
  }
}
</style>
