<!--
  EmployeeProfileCard Component
  Displays employee profile information
-->
<template>
  <q-card flat class="profile-card">
    <q-card-section>
      <div class="row items-center">
        <!-- Avatar Section -->
        <div class="col-auto">
          <q-avatar :size="props.avatarSize">
            <img
              :src="props.userImageUrl || props.defaultAvatarUrl"
              :alt="`${props.employeeName} avatar`"
              @error="handleImageError"
              @load="handleImageLoad"
              crossorigin="anonymous"
            />
          </q-avatar>
        </div>

        <!-- Employee Info Section -->
        <div class="col q-ml-md">
          <div class="employee-name">{{ props.employeeName }}</div>

          <div v-if="props.employeeRole" class="employee-detail">
            <q-icon name="badge" size="18px" class="q-mr-xs" />
            {{ props.employeeRole }}
          </div>

          <div v-if="props.employeePhone" class="employee-detail">
            <q-icon name="phone" size="18px" class="q-mr-xs" />
            {{ props.employeePhone }}
          </div>

          <div v-if="props.employeeLocation" class="employee-detail">
            <q-icon name="location_on" size="18px" class="q-mr-xs" />
            {{ props.employeeLocation }}
          </div>
        </div>
      </div>
    </q-card-section>
  </q-card>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { DEFAULT_VALUES } from '../constants'

interface Props {
  employeeName?: string | undefined
  employeeRole?: string | undefined
  employeePhone?: string | undefined
  employeeLocation?: string | undefined
  userImageUrl?: string | null | undefined
  avatarSize?: string
  defaultAvatarUrl?: string
}

const props = withDefaults(defineProps<Props>(), {
  employeeName: 'Unknown Employee',
  employeeRole: DEFAULT_VALUES.EMPLOYEE_TYPE,
  employeePhone: DEFAULT_VALUES.PHONE_NUMBER,
  employeeLocation: 'บางแสน',
  avatarSize: '80px',
  defaultAvatarUrl: DEFAULT_VALUES.AVATAR_PLACEHOLDER,
})

// Local state for image error handling
const imageError = ref(false)

/**
 * Handle image loading error
 */
const handleImageError = () => {
  imageError.value = true
  console.warn('Failed to load user image, using default avatar')
}

/**
 * Handle successful image loading
 */
const handleImageLoad = () => {
  imageError.value = false
  console.log('User image loaded successfully')
}
</script>

<style scoped>
.profile-card {
  background: rgba(209, 239, 232, 0.8);
  border-radius: 15px;
  transition: box-shadow 0.3s ease;
}

.profile-card:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.employee-name {
  font-size: 1.3rem;
  font-weight: bold;
  color: #333;
  margin-bottom: 8px;
  line-height: 1.2;
}

.employee-detail {
  display: flex;
  align-items: center;
  color: #666;
  margin-bottom: 4px;
  font-size: 0.95rem;
  line-height: 1.4;
}

.employee-detail:last-child {
  margin-bottom: 0;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .employee-name {
    font-size: 1.1rem;
  }

  .employee-detail {
    font-size: 0.9rem;
  }
}

@media (max-width: 480px) {
  .profile-card {
    padding: 12px;
  }

  .employee-name {
    font-size: 1rem;
    margin-bottom: 6px;
  }

  .employee-detail {
    font-size: 0.85rem;
    margin-bottom: 3px;
  }
}
</style>
