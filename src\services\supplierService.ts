import axios from 'axios'
import type { Supplier } from 'src/types/supplier'

const API_URL = 'http://localhost:3000/supplier'

export const supplierService = {
  async getSuppliers(): Promise<Supplier[]> {
    const response = await axios.get(API_URL)
    console.log('Loading Supplier :', response.data)
    return response.data
  },

  async deleteSupplier(id: number): Promise<void> {
    await axios.delete(`${API_URL}/${id}`)
  },

  async addSupplier(supplier: Supplier): Promise<Supplier> {
    const response = await axios.post(API_URL, supplier)
    return response.data
  },

  async updateSupplier(supplier: Supplier): Promise<Supplier> {
    const response = await axios.put(`${API_URL}/${supplier.id}`, supplier)
    return response.data
  }
  ,

  async getSuppliersByFilter(search: string, filter: string, type: string): Promise<Supplier[]> {
    const response = await axios.post(`${API_URL}/filter`, { search, filter, type })
    console.log('Filtered Suppliers:', response.data)
    return response.data
  },
}
