<template>
  <div v-if="showDebug" class="auth-debug-info">
    <q-card class="q-ma-md">
      <q-card-section>
        <div class="text-h6">🔐 Auth Debug Info</div>
      </q-card-section>

      <q-card-section>
        <div class="q-mb-sm">
          <strong>Authenticated:</strong>
          <q-chip :color="authStore.isAuthenticated ? 'green' : 'red'" text-color="white">
            {{ authStore.isAuthenticated ? 'Yes' : 'No' }}
          </q-chip>
        </div>

        <div class="q-mb-sm">
          <strong>Token Expired:</strong>
          <q-chip :color="authStore.isTokenExpired ? 'red' : 'green'" text-color="white">
            {{ authStore.isTokenExpired ? 'Yes' : 'No' }}
          </q-chip>
        </div>

        <div class="q-mb-sm">
          <strong>Time Until Expiration:</strong>
          {{ authStore.timeUntilExpiration }} minutes
        </div>

        <div class="q-mb-sm">
          <strong>Current User:</strong>
          {{ authStore.currentUser?.name || 'None' }}
        </div>

        <div class="q-mb-sm">
          <strong>Token Expiration:</strong>
          {{
            authStore.tokenExpiration
              ? new Date(authStore.tokenExpiration).toLocaleString()
              : 'None'
          }}
        </div>

        <div class="q-mb-sm">
          <strong>Current Time:</strong>
          {{ new Date().toLocaleString() }}
        </div>

        <div class="q-mb-sm">
          <strong>LocalStorage Token:</strong>
          {{ localStorageToken ? 'Present' : 'Missing' }}
        </div>

        <div class="q-mb-sm">
          <strong>LocalStorage Expiration:</strong>
          {{ localStorageExpiration || 'Missing' }}
        </div>
      </q-card-section>

      <q-card-actions>
        <q-btn
          flat
          color="primary"
          @click="refreshToken"
          :loading="refreshing"
          :disable="!authStore.token"
        >
          Refresh Token
        </q-btn>

        <q-btn
          flat
          color="secondary"
          @click="validateToken"
          :loading="validating"
          :disable="!authStore.token"
        >
          Validate Token
        </q-btn>

        <q-btn flat color="negative" @click="logout"> Logout </q-btn>
      </q-card-actions>
    </q-card>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { useAuthStore } from 'src/stores/auth/authStore'
import { useRouter } from 'vue-router'

const authStore = useAuthStore()
const router = useRouter()

const showDebug = ref(process.env.NODE_ENV === 'development')
const refreshing = ref(false)
const validating = ref(false)

const localStorageToken = computed(() => {
  if (typeof window !== 'undefined') {
    return localStorage.getItem('auth_token')
  }
  return null
})

const localStorageExpiration = computed(() => {
  if (typeof window !== 'undefined') {
    return localStorage.getItem('auth_token_expiration')
  }
  return null
})

const refreshToken = async () => {
  refreshing.value = true
  try {
    await authStore.refreshToken()
    console.log('✅ Manual token refresh successful')
  } catch (error) {
    console.error('❌ Manual token refresh failed:', error)
  } finally {
    refreshing.value = false
  }
}

const validateToken = async () => {
  validating.value = true
  try {
    const isValid = await authStore.validateToken()
    console.log('✅ Token validation result:', isValid)
  } catch (error) {
    console.error('❌ Token validation failed:', error)
  } finally {
    validating.value = false
  }
}

const logout = async () => {
  authStore.logout()
  await router.push('/login')
}
</script>

<style scoped>
.auth-debug-info {
  position: fixed;
  top: 100px;
  right: 20px;
  z-index: 9999;
  max-width: 300px;
}
</style>
