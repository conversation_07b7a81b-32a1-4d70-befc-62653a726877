<template>
  <StockNavigation></StockNavigation>

  <div class="container q-pa-md q-ma-md page-fade-in">
    <div class="row wrap-container">
      <div class="col-9 col-md-12 col-sm-12 col-xs-12 search-container">
        <div class="row items-center q-col-gutter-md">
          <div class="col-5">
            <searchComponent v-model="searchQuery" placeholder="ค้นหา" />
          </div>
          <div class="col-7">
            <DateRangePicker v-model:modelValueFrom="dateFrom" v-model:modelValueTo="dateTo" />
          </div>
        </div>
      </div>

      <!-- ส่วนของตัวกรองและปุ่ม -->
      <div class="col-4 filter-button-container">
        <FilterComponent v-model="selectedFilter" :filterOptions="filterOptions" class="q-mb-md" />
        <q-btn flat @click="openDialog()" class="add-button q-mb-md" label="สร้างการรับสินค้าใหม่" />
      </div>
    </div>

    <!-- Status Filter Component -->
    <StatusFilterComponent :prepare-data="store.grPrepare" :process-data="store.grOnProcess" :all-data="store.gr"
      :columns="columns" :pagination="pagination" @row-click="openDetailsDialog" @status-change="handleStatusChange" />
    <div class="row justify-center q-mt-lg" style="margin-top: 20px">
      <AddGRDialog v-model="addGrDetailsDialogOpen" :mode="modeAddGrDetails"></AddGRDialog>
      <GRDetailsDialog v-model="grDetailsDialogOpen" :mode="modeGrDetails"></GRDetailsDialog>
      <GRDetailsforDistribu v-model="grDetailsforDistribuDialogOpen" :mode="modeGrDetailsforDistribu">
      </GRDetailsforDistribu>
    </div>
  </div>
</template>
<script setup lang="ts">
import { onMounted, ref } from 'vue'
import StockNavigation from 'src/components/StockNavigation.vue'
import searchComponent from 'src/components/searchComponent.vue'
import DateRangePicker from 'src/components/DateRangePicker.vue'
import FilterComponent from 'src/components/filterComponent.vue'
import StatusFilterComponent from 'src/components/StatusFilterComponent.vue'
import type { QTableColumn } from 'quasar'
import AddGRDialog from 'src/components/dialog/goodsreceipt/AddGRDialog.vue'
import GRDetailsDialog from 'src/components/dialog/goodsreceipt/GRDetailsDialog.vue'
import GRDetailsforDistribu from 'src/components/dialog/goodsreceipt/GRDetailsforDistribu.vue'
import { useGoodsReceiptStore } from 'src/stores/orders/goodsreceipt'
import type { GoodsReceipt } from 'src/types/goodsReceipt'
import { useGoodsReceiptPaymentStore } from 'src/stores/orders/goodsReceiptPayment'

const pagination = ref({
  rowsPerPage: 4,
})

onMounted(async () => {
  await store.fetchGoodsReceiptByStatus()
  handleStatusChange({
    prepare: true,
    process: true,
    all: false,
  })
  // console.log(store.gr)
})

const columns = <QTableColumn[]>[
  {
    name: 'code',
    label: 'เลขที่รับสินค้า',
    field: 'code',
    align: 'left' as const,
    sortable: true,
  },
  {
    name: 'date_document',
    label: 'วันที่',
    field: (row) => {
      const date = new Date(row.date_document)
      const year = date.getFullYear()
      const month = String(date.getMonth() + 1).padStart(2, '0')
      const day = String(date.getDate()).padStart(2, '0')
      const hours = String(date.getHours()).padStart(2, '0')
      const minutes = String(date.getMinutes()).padStart(2, '0')
      const seconds = String(date.getSeconds()).padStart(2, '0')
      return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`
    },
    align: 'left' as const,
    sortable: true,
  },
  {
    name: 'po_code',
    label: 'เลขที่สั่งซื้อสินค้า',
    field: 'po_code',
    align: 'left' as const,
    sortable: true,
  },
  {
    name: 'tax_invoice_number',
    label: 'เลขที่ภาษี',
    field: 'tax_invoice_number',
    align: 'left' as const,
    sortable: true,
  },
  {
    name: 'tax_invoice_number',
    label: 'วันที่ภาษี',
    field: 'tax_invoice_number',
    align: 'left' as const,
    sortable: true,
  },
  {
    name: 'distributor',
    label: 'บริษัทจำหน่าย',
    field: (row) => (row.distributor ? row.distributor.name : ''),
    align: 'left' as const,
    sortable: true,
  },
  {
    name: 'tax_total',
    label: 'รวมเงิน',
    field: 'tax_total',
    align: 'left' as const,
    sortable: true,
  },
]

const store = useGoodsReceiptStore()
const paymentStore = useGoodsReceiptPaymentStore()
// const filteredOrders = ref(store.orders)
const dateFrom = ref('')
const dateTo = ref('')
const searchQuery = ref('')
const selectedFilter = ref<string>('')
const grDetailsDialogOpen = ref(false)
const modeGrDetails = ref('add')
const grDetailsforDistribuDialogOpen = ref(false)
const modeGrDetailsforDistribu = ref('add')
const addGrDetailsDialogOpen = ref(false)
const modeAddGrDetails = ref('add')
const filterOptions = [
  { label: 'บาร์โค้ด', value: 'barcode' },
  { label: 'รหัสสินค้า', value: 'product_code' },
  { label: 'ชื่อสินค้า', value: 'product_name' },
  { label: 'สถานที่เก็บ', value: 'storage_location' },
  { label: 'กลุ่มชื่อสามัญ', value: 'common_name_group' },
  { label: 'ข้อความเตือน', value: 'warning_message' },
]
async function openDetailsDialog(_evt: Event, row: GoodsReceipt) {
  console.log('🚀 Clicked Row Object:', row)
  await store.fetchDetailsByGR(row.id)
  await paymentStore.fetchByGRId(row.id)
  await store.fetchGoodsReceiptById(row.id)

  // Check if row has PO (Purchase Order)
  if (row.po && row.po.id && row.po.id > 0) {
    // Row has PO - open GRDetailsDialog
    grDetailsDialogOpen.value = true
    modeGrDetails.value = 'edit'
  } else {
    // Row doesn't have PO - open GRDetailsforDistribu
    grDetailsforDistribuDialogOpen.value = true
    modeGrDetailsforDistribu.value = 'edit'
  }
}
const openDialog = () => {
  addGrDetailsDialogOpen.value = true
  modeAddGrDetails.value = 'add'
}

const handleStatusChange = (statuses: { prepare: boolean, process: boolean, all: boolean }) => {
  console.log('Status changed:', statuses)
  // You can add additional logic here if needed
  // For example, filtering data or updating other components
}
</script>

<style scoped>
:deep(.q-table thead tr) {
  background-color: #91d2c1;
}

.search-container {
  flex: 1;
  min-width: 311px;
  max-width: 1042px;
}

.container {
  background-color: white;
  border-radius: 10px;
  height: 100%;
  max-height: 1500px;
}

.add-button {
  background-color: #294888;
  color: white;
  border-radius: 5px;
  width: 100%;
  min-width: 141px;
  max-width: 300px;
  height: 40px;
}

.dashboard-btn {
  background-color: #609fa3;
  color: white;
  border-radius: 10px;
  padding: 10px 20px;
}

.body-table {
  background-color: #e1edea;
}

.custom-table {
  overflow: hidden;
}

.add-button {
  background-color: #294888;
  color: white;
  border-radius: 5px;
  width: 100%;
  min-width: 141px;
  max-width: 300px;
  height: 40px;
}

.filter-button-container {
  display: flex;
  gap: 16px;
  align-items: center;
  justify-content: flex-start;
}

.wrap-container {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
  align-items: stretch;
}

/* Move carousel navigation dots further down */
:deep(.q-carousel__navigation) {
  bottom: 1px !important;
}

/* Add some bottom padding to the carousel to accommodate the moved navigation */
:deep(.q-carousel) {
  padding-bottom: 30px;
}

.filter-box {
  background-color: #c3e7dd;
  padding-left: 10px;
  padding-right: 10px;
  border-radius: 5px;
  width: 100%;
  max-width: 310px;
}

.custom-dropdown .q-item {
  background-color: #e1edea !important;
  color: black !important;
}
</style>
