<template>
  <q-dialog v-model="isOpen">
    <q-card style="max-width: 800px; width: 100%">
      <q-card-section class="row items-center justify-between">
        <div class="text-h6 text-weight-bold">การแยกหน่วยฐานของสต็อก</div>
        <q-btn icon="close" flat round dense v-close-popup />
      </q-card-section>
      <q-separator />

      <q-card-section style="overflow: auto; max-height: 80vh" class="scroll">
        <div class="gap-container">
          <div class="text-white shadow-2 container-header" style="font-weight: bold">
            รายการสินค้าที่ต้องการแยกหน่วยฐาน
          </div>
          <div class="shadow-2 container">
            <div class="q-col-gutter-md">
              <div class="col-12 q-mb-md">
                <div class="text-subtitle2 q-mb-sm">
                  กฎการแยกหน่วยฐาน : {{ selectedStock?.product?.product_name || 'Product' }} ({{
                    selectedStock?.product?.unit || 'Unit'
                  }})
                </div>

                <q-select
                  v-model="selectedConversion"
                  :options="conversions"
                  :option-label="
                    (item) =>
                      `${item.fromProduct.product_name} (${item.fromProduct.unit}) → ${item.toProduct.product_name} (${item.toProduct.unit}) × ${item.conversionRate}`
                  "
                  outlined
                  dense
                  label="กฎการแยกหน่วยฐาน"
                  :disable="conversions.length === 0"
                />

                <div v-if="conversions.length === 0" class="text-caption text-grey-6 q-mt-sm">
                  ไม่พบกฎการแยกหน่วยฐาน
                </div>
              </div>

              <div class="col-12 q-mb-md">
                <q-input
                  v-model.number="breakQuantity"
                  type="number"
                  label="จำนวนต้องการ"
                  outlined
                  dense
                  :rules="[(val) => val > 0 || 'Must be greater than 0']"
                />
              </div>
            </div>

            <q-btn
              color="primary"
              class="full-width q-my-md"
              label="สร้างกฎการแยกหน่วยฐาน"
              @click="openCreateRuleDialog"
            />
          </div>
        </div>
      </q-card-section>

      <q-card-actions align="right" class="q-pa-md">
        <q-btn
          flat
          label="Confirm"
          color="primary"
          :disable="!selectedConversion || breakQuantity <= 0"
          @click="confirmBreak"
        />
      </q-card-actions>
    </q-card>
  </q-dialog>

  <!-- Dialog for creating a new breaking rule -->
  <q-dialog v-model="createRuleDialogOpen">
    <q-card style="max-width: 800px; width: 100%">
      <!-- Header -->
      <q-card-section class="row items-center justify-between">
        <div class="text-h6" style="font-weight: bold">การแยกหน่วยฐานของสต็อก</div>
        <q-btn icon="close" flat round dense v-close-popup />
      </q-card-section>

      <!-- Section Title -->
      <div class="gap-container">
        <q-card-section class="q-pa-sm bg-primary text-white" style="border-radius: 4px 4px 0 0">
          <div class="text-subtitle1">รายละเอียดของสินค้าที่ต้องการแยกหน่วยฐาน</div>
        </q-card-section>

        <!-- Input Fields -->
        <q-card-section class="bg-blue-1" style="border-radius: 0 0 6px 6px; padding: 24px">
          <div class="column q-gutter-md">
            <!-- From Product -->
            <div style="background-color: white; border-radius: 8px; padding: 16px">
              <div class="text-subtitle2 q-mb-xs">สินค้าที่ต้องการแยกหน่วยฐาน</div>
              <q-input
                outlined
                readonly
                dense
                :model-value="`${selectedStock.product?.product_name || ''} (${selectedStock.product?.unit || 'Unit'})`"
                style="background-color: #6b8fc2; color: white"
              />
            </div>

            <!-- To Product -->
            <div style="background-color: white; border-radius: 8px; padding: 16px">
              <div class="text-subtitle2 q-mb-xs">หน่วยฐานที่ต้องการ</div>
              <q-select
                outlined
                dense
                v-model="newRule.toProduct"
                :options="availableProducts"
                :option-label="(item) => `${item.product_name} (${item.unit})`"
                :rules="[(val) => !!val || 'กรุณาเลือกหน่วยฐาน']"
                style="background-color: #6b8fc2; color: white; border-radius: 6px"
                input-class="text-white"
              />
            </div>

            <!-- Quantity -->
            <div style="background-color: white; border-radius: 8px; padding: 16px">
              <div class="text-subtitle2 q-mb-xs">จำนวนที่ต้องการ</div>
              <q-input
                outlined
                dense
                type="number"
                v-model.number="newRule.conversionRate"
                :rules="[
                  (val) => val > 0 || 'จำนวนต้องมากกว่า 0',
                  (val) => Number.isInteger(val) || 'ต้องเป็นจำนวนเต็ม',
                ]"
                style="background-color: #6b8fc2; color: white; border-radius: 6px"
              />
              <div class="text-caption q-mt-xs">
                1 {{ selectedStock.product?.product_name || '' }} =
                {{ newRule.conversionRate || '?' }}
                {{ newRule.toProduct?.product_name || 'หน่วยฐาน' }}
              </div>
            </div>
          </div>
        </q-card-section>

        <!-- Action Buttons -->
        <q-card-actions align="center" class="q-pa-md">
          <q-btn
            label="บันทึก"
            color="positive"
            unelevated
            @click="saveNewRule"
            :disable="!isNewRuleValid"
          />
        </q-card-actions>
      </div>
    </q-card>
  </q-dialog>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { useProductConversionStore } from 'src/stores/inventory/productConversion'
import { useStockStore } from 'src/stores/inventory/stock'
import { useProductStore } from 'src/stores/inventory/product'
import type { Stock } from 'src/types/stock'
import type { ProductConversion } from 'src/types/productConversion'
import type { Product } from 'src/types/product'

const props = defineProps<{
  modelValue: boolean
  stock: Stock | null
}>()

const emit = defineEmits(['update:modelValue', 'break-completed'])

const isOpen = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value),
})

const selectedStock = computed(() => props.stock || ({} as Stock))
const conversionStore = useProductConversionStore()
const stockStore = useStockStore()
const productStore = useProductStore()
const conversions = ref<ProductConversion[]>([])
const selectedConversion = ref<ProductConversion | null>(null)
const breakQuantity = ref(1) // ✅ จำนวนต้องการ
const createRuleDialogOpen = ref(false)
const newRule = ref({
  fromProductId: 0,
  toProduct: null as Product | null,
  conversionRate: 1,
})

const availableProducts = computed(() => {
  const from = selectedStock.value?.product
  if (!from) return []

  return productStore.products.filter(
    (p) => p.product_name === from.product_name && p.unit !== from.unit && p.id !== from.id,
  )
})

const isNewRuleValid = computed(() => {
  return (
    newRule.value.toProduct &&
    newRule.value.conversionRate > 0 &&
    Number.isInteger(newRule.value.conversionRate)
  )
})

watch(
  () => props.stock,
  async () => {
    if (props.stock?.product?.id) {
      await loadConversions()
    }
  },
  { immediate: true },
)

watch(
  () => isOpen.value,
  async (newValue) => {
    if (newValue) {
      await productStore.fetchProducts()
    } else {
      selectedConversion.value = null
      breakQuantity.value = 1
    }
  },
)

async function loadConversions() {
  if (!selectedStock.value?.product?.id) return

  await conversionStore.fetchAllConversions()
  await productStore.fetchProducts()

  conversions.value = conversionStore.conversions.filter(
    (c) => c.fromProduct.product_name === selectedStock.value.product.product_name,
  )
}

function openCreateRuleDialog() {
  if (selectedStock.value?.product) {
    newRule.value.fromProductId = selectedStock.value.product.id
    newRule.value.toProduct = null
    newRule.value.conversionRate = 1
    createRuleDialogOpen.value = true
  }
}

async function saveNewRule() {
  if (!isNewRuleValid.value || !selectedStock.value?.product || !newRule.value.toProduct) return

  try {
    const newConversion = await conversionStore.createConversion({
      fromProductId: selectedStock.value.product.id,
      toProductId: newRule.value.toProduct.id,
      conversionRate: newRule.value.conversionRate,
      fromProduct: selectedStock.value.product,
      toProduct: newRule.value.toProduct,
    })

    if (newConversion) {
      await loadConversions()
      createRuleDialogOpen.value = false
      selectedConversion.value = newConversion
    }
  } catch (error) {
    console.error('Error creating conversion rule:', error)
  }
}

async function confirmBreak() {
  if (!selectedConversion.value || !selectedStock.value || breakQuantity.value <= 0) return

  try {
    // ✅ ส่งแค่ body ต้องการ
    const payload = {
      toId: selectedConversion.value.toProduct?.id,
      quantity: breakQuantity.value,
      branchId: selectedStock.value.branch?.id || 1,
    }

    console.log('[BREAK PAYLOAD]', payload)

    // ✅ ใช้ ID ใส่ใน URL
    await conversionStore.breakProduct(selectedConversion.value.id, payload)

    await stockStore.fetchAllStock()
    emit('break-completed')
    isOpen.value = false
  } catch (error) {
    console.error('Error breaking stock:', error)
  }
}
</script>

<style scoped>
.container {
  background-color: #deecff;
  padding: 16px;
  border-top-left-radius: 0px;
  border-top-right-radius: 0px;
  border-bottom-left-radius: 10px;
  border-bottom-right-radius: 10px;
}

.container-header {
  background-color: #294888;
  padding: 16px;
  border-top-left-radius: 10px;
  border-top-right-radius: 10px;
  height: 55px;
}

.mini-container {
  padding-left: 10px;
  padding-right: 10px;
  background-color: white;
  border-radius: 5px;
}

.input-container {
  padding-left: 10px;
  padding-right: 10px;
  background-color: white;
  border-radius: 5px;
}

.btn-accept {
  background-color: #36b54d;
  margin-right: 10px;
}

.gap-container {
  margin-bottom: 20px;
}

.dialog-container {
  border-radius: 10px;
}

.input-container-v2 {
  padding-left: 10px;
  padding-right: 10px;
  border-radius: 5px;
  background-color: #83a7d8;
}

.input-container-v2-stock {
  width: 89px;
  padding-left: 10px;
  padding-right: 10px;
  border-radius: 5px;
  background-color: #83a7d8;
}

.width-column {
  width: 300px;
}

.width-column-groupproduct {
  width: 450px;
}

.width-column-table-1 {
  width: 200px;
}

.width-column-table-2 {
  width: 100px;
}

.column-table {
  height: 40px;
  background-color: #294888;
  color: white;
}

.column-table-2 {
  height: 40px;
  padding-left: 10px;
  padding-right: 10px;
  background-color: white;
  border: 2px solid #294888;
}
</style>
