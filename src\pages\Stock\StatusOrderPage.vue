<template>
  <StockNavigation></StockNavigation>
  <div class="container q-pa-md q-ma-md page-fade-in">
    <div class="row wrap-container q-mb-md">
      <div class="col-7 col-md-12 col-sm-12 col-xs-12 search-container">
        <SearchComponent placeholder="ค้นหา" v-model="searchTerm" />
      </div>
      <div class="col-4 filter-button-container">
        <DateRangePicker v-model:modelValueFrom="dateFrom" v-model:modelValueTo="dateTo" />
        <FilterComponent
          v-model="selectedFilter"
          :filterOptions="filterOptions"
          style="min-width: 240px"
        />
      </div>
    </div>
    <q-card flat class="custom-table">
      <q-table
        flat
        class="body-table"
        :rows="filteredOrders"
        :columns="columns"
        row-key="id"
        :pagination="pagination"
        :rows-per-page-options="[]"
        style="height: 100%; max-height: 700px"
        @row-click="handleOpenEdit"
      >
        <template #body-cell-status="props">
          <q-td class="q-gutter-x-sm" style="min-width: 100px; text-align: center">
            <q-badge
              :label="props.row.status"
              :class="{
                'yellow-card': props.row.status === 'เตรียมรายการ',
                'green-card': props.row.status === 'เสร็จสมบูรณ์',
                'default-card': props.row.status === 'ดำเนินการ',
                'red-card': props.row.status === 'ยกเลิก',
              }"
              class="q-ml-none"
              style="
                height: 30px;
                width: 100px;
                display: flex;
                align-items: center;
                justify-content: center;
                border-radius: 8px;
              "
            >
            </q-badge>
          </q-td>
        </template>
      </q-table>
    </q-card>
  </div>
  <PODetailsDialogStatus></PODetailsDialogStatus>
</template>

<script setup lang="ts">
import { parse, format } from 'date-fns'
import { onMounted, ref, watch } from 'vue'
import StockNavigation from 'src/components/StockNavigation.vue'
import SearchComponent from 'src/components/searchComponent.vue'
import FilterComponent from 'src/components/filterComponent.vue'
import DateRangePicker from 'src/components/DateRangePicker.vue'
import { usePurchaseOrderStore } from 'src/stores/orders/purchaseorder'
import type { PurchaseOrder } from 'src/types/purchaseOrder'
import PODetailsDialogStatus from 'src/components/dialog/PODetailsDialogStatus.vue'
import { useDialogPODetails } from 'src/stores/dialogs/dialog-po-details'

const store = usePurchaseOrderStore()
const dialogPODetails = useDialogPODetails()
const searchTerm = ref('')
const selectedFilter = ref<string>('')
const dateFrom = ref('')
const dateTo = ref('')
const filteredOrders = ref(store.orders)

onMounted(async () => {
  await store.fetchOrders()
  console.log('loading order:', store.orders)
})

watch(
  () => store.orders,
  () => {
    filteredOrders.value = store.orders
  },
)

watch(
  [searchTerm, selectedFilter, dateFrom, dateTo],
  async () => {
    const formattedDateFrom = dateFrom.value
      ? format(parse(dateFrom.value, 'dd/MM/yyyy', new Date()), 'yyyy-MM-dd')
      : ''
    const formattedDateTo = dateTo.value
      ? format(parse(dateTo.value, 'dd/MM/yyyy', new Date()), 'yyyy-MM-dd')
      : ''
    console.log('Formatted Date From:', formattedDateFrom)
    console.log('Formatted Date To:', formattedDateTo)
    await store.filterOrders(
      searchTerm.value,
      selectedFilter.value,
      formattedDateFrom,
      formattedDateTo,
    )
    filteredOrders.value = store.orders
  },
  { immediate: true },
)

const pagination = ref({
  rowsPerPage: 12,
})

const filterOptions = [
  { label: 'รหัสคำสั่งซื้อ', value: 'code' },
  { label: 'ชื่อผู้สั่ง', value: 'user.name' },
  { label: 'สถานะ', value: 'status' },
]

const columns = [
  {
    name: 'id',
    label: 'ลำดับ',
    field: 'id',
    align: 'left' as const,
    sortable: true,
  },
  {
    name: 'code',
    label: 'รหัสคำสั่งซื้อ',
    field: 'code',
    align: 'left' as const,
    sortable: true,
  },
  {
    name: 'date',
    label: 'วันที่สั่งซื้อ',
    field: 'date',
    align: 'left' as const,
    sortable: true,
  },
  {
    name: 'order_total',
    label: 'จำนวนสินค้า',
    field: 'order_total',
    align: 'left' as const,
    sortable: true,
  },
  {
    name: 'userName',
    label: 'ชื่อผู้สั่ง',
    field: (row: PurchaseOrder) => row.user?.name,
    align: 'left' as const,
    sortable: true,
  },
  {
    name: 'status',
    label: 'สถานะ',
    field: 'status',
    align: 'left' as const,
    sortable: true,
  },
]

async function handleOpenEdit(evt: Event, row: PurchaseOrder) {
  store.form = { ...row }
  store.formItems = { ...row }

  await store.fetchOrdersItem()

  dialogPODetails.open2('edit')
}
</script>

<style scoped>
.container {
  background-color: white;
  border-radius: 10px;
  height: 100%;
  max-height: 800px;
}

.add-button {
  background-color: #294888;
  color: white;
  border-radius: 5px;
  width: 100%;
  min-width: 141px;
  max-width: 300px;
  height: 40px;
}

:deep(.q-table thead tr) {
  background-color: #91d2c1;
}

.body-table {
  background-color: #e1edea;
}

.custom-table {
  border-radius: 10px;
  overflow: hidden;
}

.wrap-container {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
  align-items: stretch;
}

.search-container {
  flex: 1;
  min-width: 311px;
  max-width: 850px;
}

.filter-button-container {
  display: flex;
  gap: 16px;
  align-items: center;
  justify-content: flex-start;
}

@media (max-width: 1900px) {
  .wrap-container {
    flex-direction: column;
    align-items: stretch;
  }

  .search-container {
    flex: 1;
    max-width: 1500px;
  }

  .filter-button-container {
    flex-direction: row;
    width: 100%;
  }

  .text-right {
    text-align: left;
  }
}

@media (max-width: 1040px) {
  .filter-button-container {
    flex-direction: column;
    align-items: stretch;
    width: 100%;
  }
}

.yellow-card {
  background-color: #ed9b53;
  color: white;
}

.green-card {
  background-color: #439e62;
  color: white;
}

.default-card {
  background-color: #83a7d8;
  color: white;
}

.red-card {
  background-color: #b53638;
  color: white;
}
</style>
