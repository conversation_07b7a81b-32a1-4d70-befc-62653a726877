import { api } from 'src/boot/axios'
import type { StockTransferOrder } from 'src/types/stockTransferOrder'


export class StockTransferOrderService {
  static path = 'sto'

  static async getAll() {
    const res = await api.get(this.path)
    return res.data
  }

  static async getOne(id: number) {
    const res = await api.get(`${this.path}/${id}`)
    return res.data
  }
  static async getSTOById(id: number) {
    const res = await api.get(`${this.path}/${id}`)
    return res.data
  }

  static async getProductByBranch(id: number) {
    const res = await api.get(`${this.path}/product/${id}`)
    return res.data
  }

  static async getSTOByStatus(status: string) {
    const res = await api.get(`${this.path}/status/${status}`)
    return res.data
  }

  static async create(sto: Partial<StockTransferOrder>) {
    const payload = {
      ...sto,
      request_date: sto.request_date ? sto.request_date.toISOString() : new Date().toISOString(), // 💡 แปลงเป็น string ISO ตอนส่งเท่านั้น
    }
    const res = await api.post(this.path, payload)
    return res.data
  }

  static async updateOne(id: number, obj: Partial<StockTransferOrder>) {
    const res = await api.put(`${this.path}/${id}/`, obj)
    return res.data
  }
}
