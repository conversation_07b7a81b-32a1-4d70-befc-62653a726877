import { defineStore } from 'pinia'
import { api } from 'src/boot/axios'
import type { user } from 'src/types/user'

interface AuthState {
  token: string | null
  tokenExpiration: string | null
  isAuthenticated: boolean
  currentUser: user | null
  loading: boolean
  error: string | null
}

interface LoginResponse {
  token: string
  tokenExpiration: string
  id: number
  name: string
  tel: string
  role: string
  hour_work: number
  sick_level: number
  personal_leave: number
  image: string
  day_off?: string
  branch: {
    id: number
    name: string
    address: string
    contact_name: string
    contact_phone: string
  }
  processedImageUrl?: string
}

export const useAuthStore = defineStore('auth', {
  state: (): AuthState => ({
    token: null,
    tokenExpiration: null,
    isAuthenticated: false,
    currentUser: null,
    loading: false,
    error: null,
  }),

  getters: {
    /**
     * Check if token is expired
     */
    isTokenExpired: (state) => {
      if (!state.tokenExpiration) return true
      const now = new Date()
      const expiration = new Date(state.tokenExpiration)
      return now >= expiration
    },

    /**
     * Get time until token expires (in minutes)
     */
    timeUntilExpiration: (state) => {
      if (!state.tokenExpiration) return 0
      const now = new Date().getTime()
      const expiration = new Date(state.tokenExpiration).getTime()
      return Math.max(0, Math.floor((expiration - now) / (1000 * 60)))
    },

    /**
     * Check if user has specific role
     */
    hasRole: (state) => (role: string) => {
      return state.currentUser?.role === role
    },
  },

  actions: {
    /**
     * Initialize auth state from localStorage
     */
    initializeAuth() {
      try {
        const token = localStorage.getItem('auth_token')
        const tokenExpiration = localStorage.getItem('auth_token_expiration')
        const userData = localStorage.getItem('auth_user')

        if (token && tokenExpiration && userData) {
          // Check if token is still valid
          if (new Date() < new Date(tokenExpiration)) {
            this.token = token
            this.tokenExpiration = tokenExpiration
            this.currentUser = JSON.parse(userData)
            this.isAuthenticated = true

            // Set authorization header for axios
            api.defaults.headers.common['Authorization'] = `Bearer ${token}`

            console.log('✅ Auth state restored from localStorage:', {
              hasToken: !!this.token,
              hasUser: !!this.currentUser,
              userName: this.currentUser?.name,
              tokenExpiration: this.tokenExpiration,
              isAuthenticated: this.isAuthenticated,
            })
          } else {
            console.log('🔄 Token expired, clearing auth state')
            this.clearAuth()
          }
        } else {
          console.log('🔍 Missing auth data in localStorage:', {
            hasToken: !!token,
            hasTokenExpiration: !!tokenExpiration,
            hasUserData: !!userData,
          })
        }
      } catch (error) {
        console.error('❌ Error initializing auth state:', error)
        this.clearAuth()
      }
    },

    /**
     * Login user
     */
    async login(name: string, password: string) {
      this.loading = true
      this.error = null

      try {
        const response = await api.post<LoginResponse>('auth/login', { name, password })
        const data = response.data

        // Store auth data
        this.token = data.token
        this.tokenExpiration = data.tokenExpiration
        this.isAuthenticated = true

        console.log('🔍 Login response data:', {
          hasToken: !!data.token,
          tokenExpiration: data.tokenExpiration,
          tokenExpirationStored: this.tokenExpiration,
        })

        // Create user object (add password field for type compatibility)
        // eslint-disable-next-line @typescript-eslint/no-unused-vars
        const { token: _, tokenExpiration: __, ...userInfo } = data
        this.currentUser = { ...userInfo, password: '', } // Password not included in response for security

        // Persist to localStorage
        localStorage.setItem('auth_token', data.token)
        localStorage.setItem('auth_token_expiration', data.tokenExpiration)
        localStorage.setItem('auth_user', JSON.stringify(this.currentUser))

        // Set authorization header for future requests
        api.defaults.headers.common['Authorization'] = `Bearer ${data.token}`

        console.log('✅ User logged in successfully')
        return userInfo
      } catch (error: unknown) {
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        this.error = (error as any).response?.data?.message || 'Login failed'
        console.error('❌ Login failed:', error)
        throw error
      } finally {
        this.loading = false
      }
    },

    /**
     * Logout user
     */
    logout() {
      this.clearAuth()
      console.log('✅ User logged out')
    },

    /**
     * Clear authentication state
     */
    clearAuth() {
      this.token = null
      this.tokenExpiration = null
      this.isAuthenticated = false
      this.currentUser = null
      this.error = null

      // Clear localStorage
      localStorage.removeItem('auth_token')
      localStorage.removeItem('auth_token_expiration')
      localStorage.removeItem('auth_user')

      // Remove authorization header
      delete api.defaults.headers.common['Authorization']
    },

    /**
     * Refresh token
     */
    async refreshToken() {
      if (!this.token) {
        throw new Error('No token to refresh')
      }

      try {
        const response = await api.post(
          'auth/refresh',
          {},
          {
            headers: { Authorization: `Bearer ${this.token}` },
          },
        )

        const { token, tokenExpiration } = response.data

        // Update token data
        this.token = token
        this.tokenExpiration = tokenExpiration

        // Update localStorage
        localStorage.setItem('auth_token', token)
        localStorage.setItem('auth_token_expiration', tokenExpiration)

        // Update authorization header
        api.defaults.headers.common['Authorization'] = `Bearer ${token}`

        console.log('✅ Token refreshed successfully')
        return token
      } catch (error) {
        console.error('❌ Token refresh failed:', error)
        this.clearAuth()
        throw error
      }
    },

    /**
     * Validate current token
     */
    async validateToken() {
      if (!this.token) {
        return false
      }

      try {
        const response = await api.get('auth/validate', {
          headers: { Authorization: `Bearer ${this.token}` },
        })

        if (response.data.tokenValid) {
          // Update user data if needed
          // eslint-disable-next-line @typescript-eslint/no-unused-vars
          const { tokenValid, ...userInfo } = response.data
          this.currentUser = { ...userInfo, password: '' } // Add password field for type compatibility
          localStorage.setItem('auth_user', JSON.stringify(this.currentUser))
          return true
        }

        return false
      } catch (error) {
        console.error('❌ Token validation failed:', error)
        this.clearAuth()
        return false
      }
    },

    /**
     * Auto-refresh token when it's about to expire
     */
    setupTokenRefresh() {
      const checkAndRefresh = async () => {
        if (this.isAuthenticated && !this.isTokenExpired) {
          const minutesUntilExpiration = this.timeUntilExpiration

          // Refresh token when 30 minutes or less remaining
          if (minutesUntilExpiration <= 30 && minutesUntilExpiration > 0) {
            try {
              await this.refreshToken()
              console.log('🔄 Token auto-refreshed')
            } catch (error) {
              console.error('❌ Auto-refresh failed:', error)
            }
          }
        }
      }

      // Check every 5 minutes
      setInterval(
        () => {
          checkAndRefresh().catch(console.error)
        },
        5 * 60 * 1000,
      )
    },
  },
})
