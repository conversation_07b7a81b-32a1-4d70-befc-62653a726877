// import type { PageParams } from 'src/types/pagination';
import { api } from 'src/boot/axios'
import type { Stock } from 'src/types/stock'
// import { HttpStatusCode } from 'axios';

export class StockService {
  static path = 'stock'

  static async getAll() {
    const res = await api.get(this.path)
    return res.data
  }

  static async getOne(id: number) {
    const res = await api.get(`${this.path}/${id}`)
    return res.data
  }

  static async getOneByProductId(id: number) {
    const res = await api.get(`${this.path}/product/${id}`)
    return res.data
  }

  static async updateOne(id: number, obj: Partial<Stock>) {
    const res = await api.put(`${this.path}/${id}/`, obj)
    return res.data
  }
  static async getAllByFilter(search = '', filter = '', branch = '') {
    console.log(branch)
    const res = await api.post(this.path, {
      search,
      filter,
      branch,
    })
    return res.data
  }
  static async getAllByFilterDialog(search = '', filter = '', branch = '', distributor = '') {
    console.log(branch)
    const res = await api.post(this.path, {
      search,
      filter,
      branch,
      distributor
    })
    return res.data
  }
  static async getSummaryByProduct(search = '', product_id = '') {
    const res = await api.post(`${this.path}/summary`, {
      search,
      product_id
    })
    console.log(res.data)
    return res.data
  }
}
