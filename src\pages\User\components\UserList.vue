<template>
  <div>
    <h5 class="q-mt-none q-mb-md text-weight-bold">รายงานรายบุคคล</h5>

    <div class="row q-col-gutter-sm q-mb-md">
      <div class="col-8">
        <SearchComponent v-model="userSearchQuery" placeholder="ค้นหา" />
      </div>
      <div class="col-4">
        <FilterComponent v-model="userTypeFilter" :filterOptions="userTypeOptions" />
      </div>
    </div>

    <div class="user-list q-mt-md">
      <q-scroll-area style="height: 400px">
        <div v-if="filteredUsers.length > 0">
          <q-card
            v-for="user in filteredUsers"
            :key="user.id"
            flat
            bordered
            class="user-card q-mb-sm"
          >
            <q-card-section class="q-py-sm">
              <div class="row items-center no-wrap">
                <q-avatar size="48px" class="q-mr-md">
                  <img
                    v-if="userImages[user.id]"
                    :src="userImages[user.id] || ''"
                    :alt="`${user.name} avatar`"
                    @error="handleImageError($event, user.id)"
                    crossorigin="anonymous"
                  />
                  <q-icon v-else name="person" color="primary" />
                </q-avatar>
                <div>
                  <div class="text-weight-bold">{{ user.name }}</div>
                  <div class="text-caption">{{ user.role }}</div>
                  <div class="text-caption attendance-info">
                    <span class="clock-label">เข้างาน:</span>
                    <span
                      :class="{
                        'no-time':
                          !userAttendance[user.id]?.clock_in ||
                          userAttendance[user.id]?.clock_in === '-',
                      }"
                    >
                      {{ userAttendance[user.id]?.clock_in || '-' }}
                    </span>
                    <span class="clock-label q-ml-sm">ออกงาน:</span>
                    <span
                      :class="{
                        'no-time':
                          !userAttendance[user.id]?.clock_out ||
                          userAttendance[user.id]?.clock_out === '-',
                      }"
                    >
                      {{ userAttendance[user.id]?.clock_out || '-' }}
                    </span>
                  </div>
                </div>
                <q-space />
                <div class="row items-center">
                  <q-badge rounded size="xs" :color="getUserStatusColor(user.id)" class="q-mr-xs" />
                  <span class="text-caption">{{ getUserStatusText(user.id) }}</span>
                </div>
              </div>
            </q-card-section>
          </q-card>
        </div>
        <div v-else class="text-center q-pa-md text-grey">ไม่พบข้อมูล</div>
      </q-scroll-area>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, ref, watch, onMounted } from 'vue'
import SearchComponent from 'src/components/searchComponent.vue'
import FilterComponent from 'src/components/filterComponent.vue'
import { useUserStore } from 'src/stores/users/userStore'
import { UserService } from 'src/services/userService'
import { useAttendanceStore } from 'src/stores/attendance/attendance'

interface UserAttendance {
  clock_in?: string
  clock_out?: string
  user?: {
    id: number
    name?: string
  }
  [key: string]: unknown
}

// User list related refs
const userStore = useUserStore()
const attendanceStore = useAttendanceStore()
const userSearchQuery = ref('')
const userTypeFilter = ref('')
const userImages = ref<Record<number, string | null>>({})
const userAttendance = ref<Record<number, UserAttendance>>({})
const userTypeOptions = [
  { label: 'ทั้งหมด', value: '' },
  { label: 'ผู้จัดการ', value: 'Manager' },
  { label: 'พนักงาน', value: 'Employee' },
  { label: 'พนักงานพาร์ทไทม์', value: 'Part-time' },
]

// Load users when component mounts
onMounted(async () => {
  await userStore.fetchUsers()
  await loadUserImages()
  await loadUserAttendance()
})

// Load user images
const loadUserImages = async () => {
  if (userStore.users) {
    for (const user of userStore.users) {
      if (user.id) {
        try {
          const imageUrl = await UserService.getUserImageById(user.id)
          userImages.value[user.id] = imageUrl
        } catch (error) {
          console.error(`Failed to load image for user ${user.id}:`, error)
        }
      }
    }
  }
}

// Load user attendance for today
const loadUserAttendance = async () => {
  if (userStore.users) {
    for (const user of userStore.users) {
      if (user.id) {
        try {
          const attendance = await attendanceStore.fetchUserTodayAttendance(user.id)
          userAttendance.value[user.id] = attendance
        } catch (error) {
          console.error(`Failed to load attendance for user ${user.id}:`, error)
        }
      }
    }
  }
}

// Filter users based on search query and type filter
const filteredUsers = computed(() => {
  let users = userStore.users || []

  if (userSearchQuery.value) {
    const query = userSearchQuery.value.toLowerCase()
    users = users.filter(
      (user) =>
        user.name?.toLowerCase().includes(query) || user.role?.toLowerCase().includes(query),
    )
  }

  if (userTypeFilter.value) {
    users = users.filter((user) => user.role === userTypeFilter.value)
  }

  return users
})

// Watch for changes in search or filter to update the list
watch([userSearchQuery, userTypeFilter], async () => {
  if (userSearchQuery.value.trim() || userTypeFilter.value) {
    await userStore.filterUsers(userSearchQuery.value, userTypeFilter.value)
    await loadUserAttendance()
  } else {
    await userStore.fetchUsers()
    await loadUserAttendance()
  }
})

// Helper functions to determine user status
const getUserStatusColor = (userId: number): string => {
  const attendance = userAttendance.value[userId]
  if (!attendance) return 'grey'
  if (attendance.clock_in === '-' || attendance.clock_out === '-') return 'red'
  if (attendance.clock_in && attendance.clock_out) return 'green'
  if (attendance.status === 'Late') return 'orange'
  if (attendance.clock_in) return 'blue'
  return 'red'
}

const getUserStatusText = (userId: number): string => {
  const attendance = userAttendance.value[userId]
  if (!attendance) return 'ไม่ข้อมูล'
  if (attendance.clock_in === '-' || attendance.clock_out === '-') return ' ไม่เข้างาน'
  if (attendance.clock_in && attendance.clock_out) return 'เลิกงานเเล้ว'
  if (attendance.status === 'Late') return 'เกินเวลา'
  if (attendance.clock_in) return 'ตรงเวลา'
  return ' ไม่เข้างาน'
}

// Handle image loading errors
const handleImageError = (event: Event, userId: number) => {
  console.error(`❌ Image failed to load for user ${userId}`)
  const imgElement = event.target as HTMLImageElement
  if (imgElement) {
    // Hide the image and let the q-icon show instead
    imgElement.style.display = 'none'
    // Remove from userImages to show the icon
    userImages.value[userId] = null
  }
}
</script>

<style scoped>
.container-content {
  background-color: #f5f5f5;
  border-radius: 10px;
  height: 100%;
}

.user-card {
  transition: all 0.2s ease;
  border-left: 3px solid transparent;
}

.user-card:hover {
  border-left: 3px solid #c3e7dd;
  background-color: rgba(225, 237, 234, 0.5);
}

.user-list {
  border-radius: 8px;
}

.attendance-info {
  margin-top: 2px;
  font-size: 0.7rem;
}

.clock-label {
  color: #666;
}

.no-time {
  color: #999;
}
</style>
