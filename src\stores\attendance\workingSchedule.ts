import { defineStore } from 'pinia'
import { WorkingScheduleService } from 'src/services/workingScheduleService'
import type {
  WorkingSchedule,
  WorkingScheduleFormData,
  FormattedWorkingSchedule,
} from 'src/types/workingSchedule'

interface WorkingScheduleState {
  schedules: WorkingSchedule[]
  currentSchedule: WorkingSchedule | null
  loading: boolean
  error: string | null
  saving: boolean
}

export const useWorkingScheduleStore = defineStore('workingSchedule', {
  state: (): WorkingScheduleState => ({
    schedules: [],
    currentSchedule: null,
    loading: false,
    error: null,
    saving: false,
  }),

  getters: {
    // Get all active schedules
    activeSchedules: (state) => state.schedules.filter((schedule) => schedule.is_active),

    // Get schedule by user ID
    getScheduleByUserId: (state) => (userId: number) =>
      state.schedules.find((schedule) => schedule.userId === userId && schedule.is_active),

    // Check if user has a schedule
    hasSchedule: (state) => (userId: number) =>
      state.schedules.some((schedule) => schedule.userId === userId && schedule.is_active),

    // Get formatted schedule for display
    getFormattedSchedule:
      (state) =>
      (userId: number): FormattedWorkingSchedule | null => {
        const schedule = state.schedules.find((s) => s.userId === userId && s.is_active)
        if (!schedule) return null

        return {
          id: schedule.id,
          userId: schedule.userId || 0,
          userName: schedule.userName || schedule.user?.name || '',
          checkInTime: WorkingScheduleService.formatTimeForDisplay(schedule.standard_check_in_time),
          checkOutTime: WorkingScheduleService.formatTimeForDisplay(
            schedule.standard_check_out_time,
          ),
          lateThresholdMinutes: schedule.late_threshold_minutes,
          earlyThresholdMinutes: schedule.early_threshold_minutes,
          isActive: schedule.is_active,
          createdAt: schedule.created_at,
          updatedAt: schedule.updated_at,
        }
      },

    // Get current schedule formatted for form
    currentScheduleForForm: (state) => {
      if (!state.currentSchedule) return null

      return {
        checkInTime: WorkingScheduleService.formatTimeForDisplay(
          state.currentSchedule.standard_check_in_time,
        ),
        checkOutTime: WorkingScheduleService.formatTimeForDisplay(
          state.currentSchedule.standard_check_out_time,
        ),
        lateThresholdMinutes: state.currentSchedule.late_threshold_minutes,
        earlyThresholdMinutes: state.currentSchedule.early_threshold_minutes,
      }
    },

    // Check if store is in loading state
    isLoading: (state) => state.loading || state.saving,
  },

  actions: {
    // Fetch all working schedules
    async fetchAllSchedules() {
      this.loading = true
      this.error = null

      try {
        const schedules = await WorkingScheduleService.getAllSchedules()
        this.schedules = schedules
      } catch (error: unknown) {
        const errorMessage = error instanceof Error ? error.message : 'Failed to fetch schedules'
        this.error = errorMessage
        console.error('Error fetching schedules:', error)
        throw error
      } finally {
        this.loading = false
      }
    },

    // Fetch schedule for a specific user
    async fetchUserSchedule(userId: number) {
      this.loading = true
      this.error = null

      try {
        const schedule = await WorkingScheduleService.getUserSchedule(userId)

        if (schedule) {
          // Update or add to schedules array
          const existingIndex = this.schedules.findIndex((s) => s.userId === userId)
          if (existingIndex >= 0) {
            this.schedules[existingIndex] = schedule
          } else {
            this.schedules.push(schedule)
          }
          this.currentSchedule = schedule
        } else {
          this.currentSchedule = null
        }

        return schedule
      } catch (error: unknown) {
        const errorMessage =
          error instanceof Error ? error.message : 'Failed to fetch user schedule'
        this.error = errorMessage
        console.error('Error fetching user schedule:', error)
        throw error
      } finally {
        this.loading = false
      }
    },

    // Create or update a working schedule
    async saveSchedule(scheduleData: WorkingScheduleFormData) {
      this.saving = true
      this.error = null

      try {
        // Format times for API
        const formattedData: WorkingScheduleFormData = {
          ...scheduleData,
          standard_check_in_time: WorkingScheduleService.formatTimeForAPI(
            scheduleData.standard_check_in_time,
          ),
          standard_check_out_time: WorkingScheduleService.formatTimeForAPI(
            scheduleData.standard_check_out_time,
          ),
        }

        const savedSchedule = await WorkingScheduleService.createOrUpdateSchedule(formattedData)

        // Update schedules array
        const existingIndex = this.schedules.findIndex((s) => s.userId === scheduleData.userId)
        if (existingIndex >= 0) {
          this.schedules[existingIndex] = savedSchedule
        } else {
          this.schedules.push(savedSchedule)
        }

        this.currentSchedule = savedSchedule
        return savedSchedule
      } catch (error: unknown) {
        const errorMessage = error instanceof Error ? error.message : 'Failed to save schedule'
        this.error = errorMessage
        console.error('Error saving schedule:', error)
        throw error
      } finally {
        this.saving = false
      }
    },

    // Update an existing schedule
    async updateSchedule(scheduleData: WorkingScheduleFormData) {
      this.saving = true
      this.error = null

      try {
        // Format times for API
        const formattedData: WorkingScheduleFormData = {
          ...scheduleData,
          standard_check_in_time: WorkingScheduleService.formatTimeForAPI(
            scheduleData.standard_check_in_time,
          ),
          standard_check_out_time: WorkingScheduleService.formatTimeForAPI(
            scheduleData.standard_check_out_time,
          ),
        }

        const updatedSchedule = await WorkingScheduleService.updateSchedule(formattedData)

        // Update schedules array
        const existingIndex = this.schedules.findIndex((s) => s.userId === scheduleData.userId)
        if (existingIndex >= 0) {
          this.schedules[existingIndex] = updatedSchedule
        }

        this.currentSchedule = updatedSchedule
        return updatedSchedule
      } catch (error: unknown) {
        const errorMessage = error instanceof Error ? error.message : 'Failed to update schedule'
        this.error = errorMessage
        console.error('Error updating schedule:', error)
        throw error
      } finally {
        this.saving = false
      }
    },

    // Delete a working schedule
    async deleteSchedule(scheduleId: number) {
      this.saving = true
      this.error = null

      try {
        await WorkingScheduleService.deleteSchedule(scheduleId)

        // Remove from schedules array
        this.schedules = this.schedules.filter((s) => s.id !== scheduleId)

        // Clear current schedule if it was deleted
        if (this.currentSchedule?.id === scheduleId) {
          this.currentSchedule = null
        }
      } catch (error: unknown) {
        const errorMessage = error instanceof Error ? error.message : 'Failed to delete schedule'
        this.error = errorMessage
        console.error('Error deleting schedule:', error)
        throw error
      } finally {
        this.saving = false
      }
    },

    // Create default schedule for a user
    async createDefaultSchedule(userId: number, checkInTime = '09:00', checkOutTime = '17:00') {
      this.saving = true
      this.error = null

      try {
        const defaultSchedule = await WorkingScheduleService.createDefaultSchedule(
          userId,
          checkInTime,
          checkOutTime,
        )

        // Add to schedules array
        this.schedules.push(defaultSchedule)
        this.currentSchedule = defaultSchedule

        return defaultSchedule
      } catch (error: unknown) {
        const errorMessage =
          error instanceof Error ? error.message : 'Failed to create default schedule'
        this.error = errorMessage
        console.error('Error creating default schedule:', error)
        throw error
      } finally {
        this.saving = false
      }
    },

    // Clear current schedule
    clearCurrentSchedule() {
      this.currentSchedule = null
    },

    // Clear error
    clearError() {
      this.error = null
    },

    // Reset store state
    resetState() {
      this.schedules = []
      this.currentSchedule = null
      this.loading = false
      this.error = null
      this.saving = false
    },
  },
})
