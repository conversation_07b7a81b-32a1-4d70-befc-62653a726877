export interface WorkingSchedule {
  id: number
  userId?: number
  userName?: string
  standard_check_in_time: string
  standard_check_out_time: string
  late_threshold_minutes: number
  early_threshold_minutes: number
  is_active: boolean
  created_at: Date | string
  updated_at: Date | string
  user?: {
    id: number
    name: string
  }
}

export interface WorkingScheduleFormData {
  userId: number
  standard_check_in_time: string
  standard_check_out_time: string
  late_threshold_minutes?: number
  early_threshold_minutes?: number
}

export interface FormattedWorkingSchedule {
  id: number
  userId: number
  userName: string
  checkInTime: string
  checkOutTime: string
  lateThresholdMinutes: number
  earlyThresholdMinutes: number
  isActive: boolean
  createdAt: Date | string
  updatedAt: Date | string
}

// API Response types
export interface WorkingScheduleApiResponse {
  id: number
  standard_check_in_time: string
  standard_check_out_time: string
  late_threshold_minutes: number
  early_threshold_minutes: number
  is_active: boolean
  created_at: string
  updated_at: string
  user: {
    id: number
    name: string
  }
}

// Form validation interface
export interface ScheduleFormValidation {
  checkInTime: boolean
  checkOutTime: boolean
  timeOrder: boolean // check-out must be after check-in
}

// Schedule dialog props
export interface ScheduleDialogProps {
  modelValue?: boolean
  userId?: number | null
  mode?: 'create' | 'edit'
}

// Schedule dialog emits
export interface ScheduleDialogEmits {
  'update:modelValue': [value: boolean]
  success: []
  error: [message: string]
}
