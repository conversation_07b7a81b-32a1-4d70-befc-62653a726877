<template>
  <q-dialog v-model="dialogStore.isOpen">
    <q-card style="max-width: 1000px">
      <q-card-section>
        <div class="row items-center justify-between">
          <div class="text-h6 text-weight-bold">รายละเอียดสินค้า</div>
          <q-btn icon="close" @click="closeDialog" flat rounded />
        </div>
      </q-card-section>

      <q-separator />

      <q-card-section style="overflow: auto; max-height: 80vh" class="scroll">
        <!-- ชื่อ และจำนวนสินค้า -->
        <div class="gap-container">
          <div class="text-white shadow-2 container-header">
            ชื่อ และจำนวนสินค้า
            <q-toggle
              v-model="store.form.isactive"
              label="Active"
              class="input-container text-black q-my-sm q-pr-sm q-ml-sm mini-container"
              :disable="isReadOnly"
            />
          </div>
          <div class="shadow-2 container">
            <div class="row q-col-gutter-md">
              <div class="col-2 flex flex-center">รหัสสินค้า *</div>
              <div class="col-4">
                <q-input
                  class="input-container"
                  v-model="store.form.product_code"
                  dense
                  borderless
                  :readonly="isReadOnly"
                />
              </div>
              <div class="col-2 flex flex-center">ชื่อสามัญ</div>
              <div class="col-4">
                <q-input
                  class="input-container"
                  v-model="store.form.generic_name"
                  dense
                  borderless
                  :readonly="isReadOnly"
                />
              </div>
              <div class="col-2 flex flex-center">ชื่อสินค้า *</div>
              <div class="col-4">
                <q-input
                  class="input-container"
                  v-model="store.form.product_name"
                  dense
                  borderless
                  :readonly="isReadOnly"
                />
              </div>
              <div class="col-2 flex flex-center">ทุนมาตรฐาน *</div>
              <div class="col-4">
                <q-input
                  class="input-container"
                  v-model="store.form.standard_cost"
                  dense
                  borderless
                  type="number"
                  :readonly="isReadOnly"
                />
              </div>
            </div>
          </div>
        </div>
        <!-- ข้อความอธิบายสินค้า -->
        <div class="gap-container">
          <div class="text-white shadow-2 container-header row items-center">
            <div>ข้อความอธิบายสินค้า</div>
          </div>
          <div class="shadow-2 container">
            <div class="row justify-between">
              <div class="col-3 mini-container width-column">
                <div class="row items-center q-my-sm">
                  <label class="col-4 text-left q-pr-md">สถานที่เก็บ</label>
                  <q-input
                    dense
                    borderless
                    class="input-container-v2 col-8 border-0"
                    v-model="store.form.storage_location"
                    :readonly="isReadOnly"
                  />
                </div>

                <div class="row items-center q-mb-sm">
                  <label class="col-4 text-left q-pr-md">Stock</label>
                  <q-input
                    dense
                    borderless
                    class="input-container-v2-stock col-3 q-mr-sm text-right"
                    v-model="store.form.stock_min"
                    label="min"
                    :readonly="isReadOnly"
                  />
                  <q-input
                    dense
                    borderless
                    class="input-container-v2-stock col-3"
                    v-model="store.form.stock_max"
                    label="max"
                    :readonly="isReadOnly"
                  />
                </div>

                <div class="row items-center q-mb-sm">
                  <label class="col-4 text-left q-pr-md">ขนาดบรรจุ</label>
                  <q-input
                    dense
                    borderless
                    class="input-container-v2 col-8"
                    v-model="store.form.packing_size"
                    :readonly="isReadOnly"
                  />
                </div>

                <div class="row items-center q-mb-sm">
                  <label class="col-4 text-left q-pr-md">Reg No.</label>
                  <q-input
                    dense
                    borderless
                    class="input-container-v2 col-8"
                    v-model="store.form.reg_no"
                    :readonly="isReadOnly"
                  />
                </div>
              </div>

              <!-- คอลัมน์กลาง: บริษัทที่ผลิต + ช่องใช้ + หมายเหตุราคาทุน -->
              <div class="col-3 width-column">
                <q-select
                  v-model="store.form.manufacturer.name"
                  :options="manufacturers"
                  option-value="id"
                  option-label="label"
                  dense
                  borderless
                  label="บริษัทที่ผลิต *"
                  class="input-container"
                  emit-value
                  map-options
                  :disable="isReadOnly"
                  @update:model-value="
                    (value) => (store.form.manufacturer = { ...store.form.manufacturer, id: value })
                  "
                />
                <div class="q-mt-md">ข้อบ่งใช้</div>
                <q-input
                  v-model="store.form.indications"
                  dense
                  borderless
                  class="input-container"
                  type="textarea"
                  :readonly="isReadOnly"
                />
              </div>

              <!-- คอลัมน์ขวา: บริษัทจำหน่าย + คำเตือน + ข้อความแจ้งเตือน -->
              <div class="col-3 width-column">
                <q-select
                  v-model="store.form.distributor.name"
                  :options="distributors"
                  option-value="id"
                  option-label="label"
                  dense
                  borderless
                  label="บริษัทจำหน่าย *"
                  class="input-container"
                  emit-value
                  map-options
                  :disable="isReadOnly"
                  @update:model-value="
                    (value) => (store.form.distributor = { ...store.form.distributor, id: value })
                  "
                />
                <div class="q-mt-md text-red">คำเตือน</div>
                <q-input
                  v-model="store.form.warnings"
                  dense
                  borderless
                  class="input-container"
                  type="textarea"
                  :readonly="isReadOnly"
                />
              </div>
            </div>

            <!-- แถวที่ 2: หมายเหตุการสั่งซื้อ -->
            <div class="row justify-between q-mt-md">
              <div class="col-3 width-column">
                <div>หมายเหตุการสั่งซื้อ</div>
                <q-input
                  v-model="store.form.purchase_notes"
                  dense
                  borderless
                  class="input-container"
                  :readonly="isReadOnly"
                />
              </div>
              <div class="col-3 width-column">
                <div>หมายเหตุราคาทุน</div>
                <q-input
                  v-model="store.form.cost_notes"
                  dense
                  borderless
                  class="input-container"
                  :readonly="isReadOnly"
                />
              </div>
              <div class="col-3 width-column">
                <div>ข้อความแจ้งเตือนเมื่อขาย</div>
                <q-input
                  v-model="store.form.sales_alert_message"
                  dense
                  borderless
                  class="input-container"
                  :readonly="isReadOnly"
                />
              </div>
            </div>
          </div>
        </div>
        <!-- วันที่ผลิตและหมดอายุ -->
        <div class="gap-container">
          <div class="text-white shadow-2 container-header row items-center">
            <div>วันที่ผลิตและหมดอายุ</div>
          </div>
          <div class="shadow-2 container">
            <div class="row q-col-gutter-md">
              <!-- วันที่ผลิต -->
              <div class="col-6">
                <div class="row items-center q-mb-sm">
                  <div class="col-4 text-left q-pr-md">
                    <label>วันที่ผลิต</label>
                  </div>
                  <div class="col-8">
                    <q-input
                      v-model="store.form.manufactureDate"
                      dense
                      borderless
                      type="date"
                      class="input-container-v2"
                      :readonly="isReadOnly"
                    />
                  </div>
                </div>
              </div>

              <!-- วันหมดอายุ -->
              <div class="col-6">
                <div class="row items-center q-mb-sm">
                  <div class="col-4 text-left q-pr-md">
                    <label>วันหมดอายุ</label>
                  </div>
                  <div class="col-8">
                    <q-input
                      v-model="store.form.expirationDate"
                      dense
                      borderless
                      type="date"
                      class="input-container-v2"
                      :readonly="isReadOnly"
                    />
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- กลุ่มสินค้า -->
        <div class="gap-container">
          <div class="text-white shadow-2 container-header row items-center">
            <div>กลุ่มสินค้า</div>
          </div>
          <div class="shadow-2 container">
            <div class="row justify-between">
              <div class="col-5 width-column-groupproduct">
                <div>กลุ่มชื่อสามัญ</div>
                <q-input
                  class="input-container"
                  v-model="store.form.generic_group"
                  dense
                  borderless
                  :readonly="isReadOnly"
                >
                </q-input>
                <div class="q-mt-md">กลุ่มรายงานพิเศษ *</div>
                <q-select
                  v-model="store.form.special_report_group.name"
                  :options="specialReportGroupOptions"
                  option-value="id"
                  option-label="label"
                  dense
                  borderless
                  label=""
                  class="input-container"
                  emit-value
                  map-options
                  :disable="isReadOnly"
                  @update:model-value="
                    (value) =>
                      (store.form.special_report_group = {
                        ...store.form.special_report_group,
                        id: value,
                      })
                  "
                />
              </div>
              <div class="col-5 width-column-groupproduct">
                <div>กลุ่มสินค้า *</div>
                <q-select
                  v-model="store.form.product_group.name"
                  :options="productGroupOptions"
                  option-value="id"
                  option-label="label"
                  dense
                  borderless
                  label=""
                  class="input-container"
                  emit-value
                  map-options
                  :disable="isReadOnly"
                  @update:model-value="
                    (value) =>
                      (store.form.product_group = { ...store.form.product_group, id: value })
                  "
                />
                <div class="q-mt-md">กลุ่มควบคุมการขาย(ขายส่ง)</div>
                <q-input
                  class="input-container"
                  v-model="store.form.wholesale_control_group"
                  dense
                  borderless
                  :readonly="isReadOnly"
                >
                </q-input>
              </div>
            </div>
          </div>
        </div>
        <!-- หน่วยและราคาสินค้า -->
        <div>
          <div class="text-white shadow-2 container-header row items-center">
            <div>หน่วยและราคาสินค้า</div>
          </div>
          <div class="shadow-2 container">
            <div class="row justify-between">
              <div class="col-1 width-column-table-1">
                <div class="row">
                  <div class="col-12" style="height: 56px"></div>
                  <div class="col-4 flex flex-center q-mb-md">หน่วย</div>
                  <div class="col-8 q-mb-md">
                    <q-input
                      class="input-container"
                      v-model="store.form.unit"
                      dense
                      borderless
                      :readonly="isReadOnly"
                    />
                  </div>
                  <div class="col-4 flex flex-center">บาร์โค้ด</div>
                  <div class="col-8">
                    <q-input
                      class="input-container"
                      v-model="store.form.barcode"
                      dense
                      borderless
                      :readonly="isReadOnly"
                    />
                  </div>
                </div>
              </div>
              <div class="col-1 width-column-table-2">
                <div class="row">
                  <div class="col-12 q-mb-md flex flex-center column-table" style="height: 40px">
                    อนุญาตขาย
                  </div>
                  <div
                    class="col-12 q-mb-md"
                    style="height: 40px; background-color: #2a97af; color: white"
                  >
                    <q-checkbox v-model="store.form.retail" label="ขายปลีก" :disable="isReadOnly" />
                  </div>
                  <div class="col-12" style="height: 40px; background-color: #865b94; color: white">
                    <q-checkbox
                      v-model="store.form.wholesale"
                      label="ขายส่ง"
                      :disable="isReadOnly"
                    />
                  </div>
                </div>
              </div>
              <div class="col-1 width-column-table-2">
                <div class="row">
                  <div class="col-12 q-mb-md flex flex-center column-table" style="height: 40px">
                    ระดับที่ 1
                  </div>
                  <div class="col-12 q-mb-md">
                    <q-input
                      class="column-table-2"
                      v-model="store.form.retail1"
                      dense
                      borderless
                      type="number"
                      :readonly="isReadOnly"
                    />
                  </div>
                  <div class="col-12">
                    <q-input
                      class="column-table-2"
                      v-model="store.form.wholesale1"
                      dense
                      borderless
                      type="number"
                      :readonly="isReadOnly"
                    />
                  </div>
                </div>
              </div>
              <div class="col-1 width-column-table-2">
                <div class="row">
                  <div class="col-12 q-mb-md flex flex-center column-table" style="height: 40px">
                    ระดับที่ 2
                  </div>
                  <div class="col-12 q-mb-md">
                    <q-input
                      class="column-table-2"
                      v-model="store.form.retail2"
                      dense
                      borderless
                      type="number"
                      :readonly="isReadOnly"
                    />
                  </div>
                  <div class="col-12">
                    <q-input
                      class="column-table-2"
                      v-model="store.form.wholesale2"
                      dense
                      borderless
                      type="number"
                      :readonly="isReadOnly"
                    />
                  </div>
                </div>
              </div>
              <div class="col-1 width-column-table-2">
                <div class="row">
                  <div class="col-12 q-mb-md flex flex-center column-table" style="height: 40px">
                    ระดับที่ 3
                  </div>
                  <div class="col-12 q-mb-md">
                    <q-input
                      class="column-table-2"
                      v-model="store.form.retail3"
                      dense
                      borderless
                      type="number"
                      :readonly="isReadOnly"
                    />
                  </div>
                  <div class="col-12">
                    <q-input
                      class="column-table-2"
                      v-model="store.form.wholesale3"
                      dense
                      borderless
                      type="number"
                      :readonly="isReadOnly"
                    />
                  </div>
                </div>
              </div>
              <div class="col-1 width-column-table-2">
                <div class="row">
                  <div class="col-12 q-mb-md flex flex-center column-table" style="height: 40px">
                    ระดับที่ 4
                  </div>
                  <div class="col-12 q-mb-md">
                    <q-input
                      class="column-table-2"
                      v-model="store.form.retail4"
                      dense
                      borderless
                      type="number"
                      :readonly="isReadOnly"
                    />
                  </div>
                  <div class="col-12">
                    <q-input
                      class="column-table-2"
                      v-model="store.form.wholesale4"
                      dense
                      borderless
                      type="number"
                      :readonly="isReadOnly"
                    />
                  </div>
                </div>
              </div>
              <div class="col-1 width-column-table-2">
                <div class="row">
                  <div class="col-12 q-mb-md flex flex-center column-table" style="height: 40px">
                    ระดับที่ 5
                  </div>
                  <div class="col-12 q-mb-md">
                    <q-input
                      class="column-table-2"
                      v-model="store.form.retail5"
                      dense
                      borderless
                      type="number"
                      :readonly="isReadOnly"
                    />
                  </div>
                  <div class="col-12">
                    <q-input
                      class="column-table-2"
                      v-model="store.form.wholesale5"
                      dense
                      borderless
                      type="number"
                      :readonly="isReadOnly"
                    />
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </q-card-section>
      <q-card-actions align="right">
        <q-btn
          class="btn-accept"
          dense
          flat
          label="บันทึก"
          @click="saveDialog"
          :disabled="!isFormValid || isReadOnly"
        />
      </q-card-actions>
    </q-card>
  </q-dialog>
</template>

<script setup lang="ts">
import { useDialogStore } from 'src/stores/dialogs/dialog-store'
import { useProductStore } from 'src/stores/inventory/product'
import { useSupplierStore } from 'src/stores/inventory/supplier'
import { computed, onMounted } from 'vue'

const store = useProductStore()
const supplierStore = useSupplierStore()
const dialogStore = useDialogStore()
const isReadOnly = computed(() => dialogStore.mode === 'view')

onMounted(async () => {
  await supplierStore.loadSuppliers()
  await store.fetchProductGroups()
  await store.fetchSpecialReportGroups()
  store.resetForm()
})

const saveProduct = async () => {
  try {
    if (store.form.id) {
      console.log('update')
      await store.updateProduct()
    } else {
      console.log('add')
      await store.addProduct()
    }

    return true
  } catch (error) {
    console.error('Error saving product:', error)
    return false
  }
}

const closeDialog = async () => {
  store.resetForm()
  dialogStore.close()
  await store.fetchProducts()
}

const saveDialog = async () => {
  const success = await saveProduct()
  if (success) {
    if (dialogStore.mode === 'add') {
      store.resetForm()
    }
    dialogStore.isOpen = false
    await store.fetchProducts()
  }
}

const isFormValid = computed(() => {
  return (
    !!store.form.product_code &&
    !!store.form.product_name &&
    !!store.form.standard_cost &&
    !!store.form.manufacturer?.id &&
    !!store.form.distributor?.id &&
    !!store.form.product_group?.id &&
    !!store.form.special_report_group?.id
  )
})

// const validateExpirationDate = (value: string) => {
//   if (!value || !store.form.manufactureDate) return true

//   const manufactureDate = new Date(store.form.manufactureDate)
//   const expirationDate = new Date(value)

//   if (expirationDate <= manufactureDate) {
//     return 'วันหมดอายุต้องมากกว่าวันที่ผลิต'
//   }

//   return true
// }

const manufacturers = computed(() =>
  supplierStore.suppliers
    .filter((supplier) => supplier.type?.id === 1)
    .map((supplier) => ({
      id: supplier.id,
      label: supplier.name,
    })),
)

const distributors = computed(() =>
  supplierStore.suppliers
    .filter((supplier) => supplier.type?.id === 2)
    .map((supplier) => ({
      id: supplier.id,
      label: supplier.name,
    })),
)

const productGroupOptions = computed(() =>
  store.productGroups.map((group) => ({
    id: group.id,
    label: group.name,
  })),
)

const specialReportGroupOptions = computed(() =>
  store.specialReportGroups.map((spegroup) => ({
    id: spegroup.id,
    label: spegroup.name,
  })),
)
</script>

<style scoped>
.container {
  background-color: #deecff;
  padding: 16px;
  border-top-left-radius: 0px;
  border-top-right-radius: 0px;
  border-bottom-left-radius: 10px;
  border-bottom-right-radius: 10px;
}

.container-header {
  background-color: #294888;
  padding-left: 16px;
  border-top-left-radius: 10px;
  border-top-right-radius: 10px;
  height: 55px;
}

.mini-container {
  padding-left: 10px;
  padding-right: 10px;
  background-color: white;
  border-radius: 5px;
}

.input-container {
  padding-left: 10px;
  padding-right: 10px;
  background-color: white;
  border-radius: 5px;
}

.btn-accept {
  background-color: #36b54d;
  margin-right: 10px;
}

.gap-container {
  margin-bottom: 20px;
}

.dialog-container {
  border-radius: 10px;
}

.input-container-v2 {
  padding-left: 10px;
  padding-right: 10px;
  border-radius: 5px;
  background-color: #83a7d8;
}

.input-container-v2-stock {
  width: 89px;
  padding-left: 10px;
  padding-right: 10px;
  border-radius: 5px;
  background-color: #83a7d8;
}

.width-column {
  width: 300px;
}

.width-column-groupproduct {
  width: 450px;
}

.width-column-table-1 {
  width: 200px;
}

.width-column-table-2 {
  width: 100px;
}

.column-table {
  height: 40px;
  background-color: #294888;
  color: white;
}

.column-table-2 {
  height: 40px;
  padding-left: 10px;
  padding-right: 10px;
  background-color: white;
  border: 2px solid #294888;
}
</style>
