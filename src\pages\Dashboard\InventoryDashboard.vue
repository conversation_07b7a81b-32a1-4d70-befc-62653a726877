<template>
  <q-page class="q-pa-md">
    <div class="text-h4 q-mb-md">📊 Inventory Overview Dashboard</div>

    <div class="row q-gutter-md">
      <!-- Stock by Branch Chart -->
      <div class="col-12 col-md-6">
        <q-card class="full-height">
          <q-card-section>
            <div class="text-h6">Stock by Branch</div>
            <canvas ref="branchChartRef"></canvas>
          </q-card-section>
        </q-card>
      </div>

      <!-- Top Products Chart -->
      <div class="col-12 col-md-6">
        <q-card class="full-height">
          <q-card-section>
            <div class="text-h6">Top Remaining Products</div>
            <canvas ref="productsChartRef"></canvas>
          </q-card-section>
        </q-card>
      </div>

      <!-- Value by Group Chart -->
      <div class="col-12">
        <q-card>
          <q-card-section>
            <div class="text-h6">Inventory Value by Product Group</div>
            <canvas ref="groupChartRef"></canvas>
          </q-card-section>
        </q-card>
      </div>
    </div>
  </q-page>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { Chart, registerables } from 'chart.js'
import { api } from 'src/boot/axios'

// Register Chart.js plugins
Chart.register(...registerables)

// -----------------------------
// Interfaces (No `any` used)
// -----------------------------

interface BranchStock {
  branch_name: string
  total_remaining: number
  total_value: number
}

interface TopProduct {
  product_name: string
  total_remaining: number
}

interface GroupValue {
  group_name: string
  total_value: number
}

// -----------------------------
// Refs for Chart.js canvas
// -----------------------------
const branchChartRef = ref<HTMLCanvasElement>()
const productsChartRef = ref<HTMLCanvasElement>()
const groupChartRef = ref<HTMLCanvasElement>()

// -----------------------------
// Chart 1: Stock by Branch
// -----------------------------
const createBranchChart = async () => {
  try {
    const response = await api.get<BranchStock[]>('/dashboard/inventory/by-branch')
    const data = response.data

    new Chart(branchChartRef.value!, {
      type: 'bar',
      data: {
        labels: data.map((item) => item.branch_name),
        datasets: [
          {
            label: 'Total Stock',
            data: data.map((item) => item.total_remaining),
            backgroundColor: '#26A69A',
          },
          {
            label: 'Total Value',
            data: data.map((item) => item.total_value),
            backgroundColor: '#AB47BC',
            yAxisID: 'y1',
          },
        ],
      },
      options: {
        responsive: true,
        scales: {
          y: { beginAtZero: true },
          y1: { type: 'linear', display: true, position: 'right' },
        },
      },
    })
  } catch (error) {
    console.error('Error creating branch chart:', error)
  }
}

// -----------------------------
// Chart 2: Top Remaining Products
// -----------------------------
const createProductsChart = async () => {
  try {
    const response = await api.get<TopProduct[]>(
      '/dashboard/inventory/top-remaining-products?limit=10',
    )
    const data = response.data

    new Chart(productsChartRef.value!, {
      type: 'doughnut',
      data: {
        labels: data.map((item) => item.product_name),
        datasets: [
          {
            data: data.map((item) => item.total_remaining),
            backgroundColor: [
              '#26A69A',
              '#AB47BC',
              '#4CAF50',
              '#FF9800',
              '#F44336',
              '#2196F3',
              '#9C27B0',
              '#607D8B',
              '#795548',
              '#E91E63',
            ],
          },
        ],
      },
      options: {
        responsive: true,
        plugins: {
          legend: { position: 'bottom' },
        },
      },
    })
  } catch (error) {
    console.error('Error creating products chart:', error)
  }
}

// -----------------------------
// Chart 3: Inventory Value by Group
// -----------------------------
const createGroupChart = async () => {
  try {
    const response = await api.get<GroupValue[]>('/dashboard/inventory/value-by-group')
    const data = response.data

    new Chart(groupChartRef.value!, {
      type: 'bar',
      data: {
        labels: data.map((item) => item.group_name),
        datasets: [
          {
            label: 'Total Value',
            data: data.map((item) => item.total_value),
            backgroundColor: '#26A69A',
          },
        ],
      },
      options: {
        responsive: true,
        scales: {
          y: { beginAtZero: true },
        },
      },
    })
  } catch (error) {
    console.error('Error creating group chart:', error)
  }
}

// -----------------------------
// Lifecycle: Mounted
// -----------------------------
onMounted(async () => {
  await createBranchChart()
  await createProductsChart()
  await createGroupChart()
})
</script>
