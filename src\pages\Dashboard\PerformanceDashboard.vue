<template>
  <q-page class="q-pa-md">
    <div class="text-h4 q-mb-md">⚡ Performance Dashboard</div>

    <div class="row q-gutter-md">
      <!-- Top On-Time Suppliers Chart -->
      <div class="col-12 col-md-6">
        <q-card class="full-height">
          <q-card-section>
            <div class="text-h6">Top On-Time Suppliers</div>
            <canvas ref="onTimeSuppliersChartRef"></canvas>
          </q-card-section>
        </q-card>
      </div>

      <!-- Slow Products Chart -->
      <div class="col-12 col-md-6">
        <q-card class="full-height">
          <q-card-section>
            <div class="text-h6">Slow Moving Products</div>
            <canvas ref="slowProductsChartRef"></canvas>
          </q-card-section>
        </q-card>
      </div>

      <!-- Supplier Efficiency Chart -->
      <div class="col-12">
        <q-card>
          <q-card-section>
            <div class="text-h6">Supplier Delivery Efficiency</div>
            <canvas ref="supplierEfficiencyChartRef"></canvas>
          </q-card-section>
        </q-card>
      </div>
    </div>
  </q-page>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { Chart, registerables } from 'chart.js'
import type { TooltipItem } from 'chart.js'
import { api } from 'src/boot/axios'

Chart.register(...registerables)

// --- Refs ---
const onTimeSuppliersChartRef = ref<HTMLCanvasElement>()
const slowProductsChartRef = ref<HTMLCanvasElement>()
const supplierEfficiencyChartRef = ref<HTMLCanvasElement>()

// --- Interfaces ---
interface OnTimeSupplier {
  supplier_name: string
  on_time_ratio: number
}

interface SlowProduct {
  product_name: string
  total_ordered: number
  avg_receiving_days: number
}

interface SupplierEfficiency {
  supplier_name: string
  avg_delivery_days: number
  efficiency_rating: string // 🔄 เปลี่ยนจาก union type เพื่อแก้ warning
}

// --- Chart Functions ---
const createOnTimeSuppliersChart = async () => {
  try {
    const response = await api.get<OnTimeSupplier[]>('/dashboard/performance/top-on-time-suppliers')
    const data = response.data.slice(0, 10)

    new Chart(onTimeSuppliersChartRef.value!, {
      type: 'bar',
      data: {
        labels: data.map((item) => item.supplier_name),
        datasets: [
          {
            label: 'On-Time Ratio (%)',
            data: data.map((item) => item.on_time_ratio),
            backgroundColor: '#4CAF50',
          },
        ],
      },
      options: {
        responsive: true,
        indexAxis: 'y',
        scales: {
          x: { beginAtZero: true, max: 100 },
        },
      },
    })
  } catch (error) {
    console.error('Error creating on-time suppliers chart:', error)
  }
}

const createSlowProductsChart = async () => {
  try {
    const response = await api.get<SlowProduct[]>('/dashboard/performance/slow-products')
    const data = response.data.slice(0, 10)

    new Chart(slowProductsChartRef.value!, {
      type: 'scatter',
      data: {
        datasets: [
          {
            label: 'Products',
            data: data.map((item) => ({
              x: item.total_ordered,
              y: item.avg_receiving_days,
              label: item.product_name,
            })),
            backgroundColor: '#F44336',
            borderColor: '#D32F2F',
          },
        ],
      },
      options: {
        responsive: true,
        plugins: {
          tooltip: {
            callbacks: {
              label: (context: TooltipItem<'scatter'>) => {
                const point = context.raw as {
                  x: number
                  y: number
                  label: string
                }
                return `${point.label}: ${point.x} ordered, ${point.y} days avg`
              },
            },
          },
        },
        scales: {
          x: {
            title: { display: true, text: 'Total Ordered' },
            beginAtZero: true,
          },
          y: {
            title: { display: true, text: 'Avg Receiving Days' },
            beginAtZero: true,
          },
        },
      },
    })
  } catch (error) {
    console.error('Error creating slow products chart:', error)
  }
}

const createSupplierEfficiencyChart = async () => {
  try {
    const response = await api.get<SupplierEfficiency[]>(
      '/dashboard/performance/supplier-efficiency',
    )
    const data = response.data

    const colors: Record<string, string> = {
      Excellent: '#4CAF50',
      Good: '#8BC34A',
      Average: '#FF9800',
      Poor: '#F44336',
    }

    new Chart(supplierEfficiencyChartRef.value!, {
      type: 'bar',
      data: {
        labels: data.map((item) => item.supplier_name),
        datasets: [
          {
            label: 'Avg Delivery Days',
            data: data.map((item) => item.avg_delivery_days),
            backgroundColor: data.map((item) => colors[item.efficiency_rating] || '#607D8B'),
          },
        ],
      },
      options: {
        responsive: true,
        plugins: {
          legend: {
            display: true,
            labels: {
              generateLabels: () => {
                return Object.entries(colors).map(([rating, color]) => ({
                  text: rating,
                  fillStyle: color,
                  strokeStyle: color,
                  lineWidth: 1,
                }))
              },
            },
          },
        },
        scales: {
          y: {
            title: { display: true, text: 'Days' },
            beginAtZero: true,
          },
        },
      },
    })
  } catch (error) {
    console.error('Error creating supplier efficiency chart:', error)
  }
}

// --- Lifecycle ---
onMounted(async () => {
  await createOnTimeSuppliersChart()
  await createSlowProductsChart()
  await createSupplierEfficiencyChart()
})
</script>
