import { api } from 'src/boot/axios';
import type { LeaveRequest } from 'src/types/leaveRequest';

export class LeaveRequestService {
  static path = 'leave-request';

  // Create a new leave request
  static async create(userId: number, leaveDate: string, leaveType: string, reason?: string) {
    const response = await api.post(`${this.path}/create`, {
      userId,
      leaveDate,
      leaveType,
      reason
    });
    return response.data;
  }

  // Get leave requests for a specific user
  static async getUserLeaveRequests(userId: number): Promise<LeaveRequest[]> {
    const response = await api.get(`${this.path}/user/${userId}`);
    return response.data;
  }

  // Get all leave requests (admin only)
  static async getAllLeaveRequests(): Promise<LeaveRequest[]> {
    const response = await api.get(`${this.path}/all`);
    return response.data;
  }

  // Update user's day off
  static async updateDayOff(userId: number, dayOff: string) {
    const response = await api.post(`${this.path}/update-day-off`, {
      userId,
      dayOff
    });
    return response.data;
  }
}