/**
 * Utility functions for User pages
 */

import { date } from 'quasar'
import {
  THAI_MONTHS,
  THAI_DAYS_SHORT,
  DAY_LETTERS,
  DATE_FORMATS,
  TIME_FORMAT_REGEX,
} from '../constants'
import type { WeekDay, MapLocation } from '../types'

/**
 * Format date for display with Thai localization
 */
export const formatThaiDate = (dateValue: Date): string => {
  const day = dateValue.getDate()
  const month = THAI_MONTHS[dateValue.getMonth()]
  const year = dateValue.getFullYear() + 543 // Convert to Buddhist Era
  return `${day} ${month} ${year}`
}

/**
 * Format current time and date for display
 */
export const formatCurrentDateTime = () => {
  const now = new Date()
  const currentTime = date.formatDate(now, DATE_FORMATS.TIME)
  const currentDate = date.formatDate(now, DATE_FORMATS.DATE_DISPLAY, {
    daysShort: [...THAI_DAYS_SHORT],
    monthsShort: [...THAI_MONTHS],
  })

  return { currentTime, currentDate }
}

/**
 * Generate week days array with today in center
 */
export const generateWeekDays = (): WeekDay[] => {
  const today = new Date()

  return Array.from({ length: 7 }, (_, index) => {
    // Calculate days with today in the center (index 3)
    const dayOffset = index - 3
    const day = new Date(today)
    day.setDate(today.getDate() + dayOffset)

    const isToday = dayOffset === 0
    const dayOfWeek = day.getDay() // 0 = Sunday, 1 = Monday, etc.

    return {
      letter: DAY_LETTERS[dayOfWeek] || 'S',
      number: day.getDate().toString(),
      isToday,
    }
  })
}

/**
 * Parse time string to display format
 */
export const parseTimeForDisplay = (rawTime: string | null | undefined): string => {
  if (!rawTime) return '-'

  // Check if the raw value is already in HH:MM format
  if (TIME_FORMAT_REGEX.test(rawTime)) {
    return rawTime.split(':').slice(0, 2).join(':')
  }

  try {
    const parsed = new Date(rawTime)
    if (!isNaN(parsed.getTime())) {
      return date.formatDate(parsed, DATE_FORMATS.TIME)
    }
  } catch (error) {
    console.warn('⚠️ Error parsing time:', error)
  }

  console.warn('⚠️ Invalid time format:', rawTime)
  return '-'
}

/**
 * Calculate work hours from attendance data
 */
export const calculateWorkHours = (
  clockIn: string | null | undefined,
  clockOut: string | null | undefined,
  workDuration?: number | null,
): string | number => {
  // If no clock in time, return '-'
  if (!clockIn) return '-'

  // If checked in but not checked out, return '-' (incomplete session)
  if (clockIn && !clockOut) return '-'

  // If we have work duration from API, use it (already in hours)
  if (clockIn && clockOut && workDuration) {
    return workDuration
  }

  // Fallback: calculate manually if we have both times but no work_duration
  if (clockIn && clockOut && !workDuration) {
    try {
      const checkInTime = new Date(clockIn)
      const checkOutTime = new Date(clockOut)
      const diffInMs = checkOutTime.getTime() - checkInTime.getTime()
      return Math.round(diffInMs / (1000 * 60 * 60))
    } catch (error) {
      console.error('Error calculating work hours:', error)
      return '-'
    }
  }

  return '-'
}

/**
 * Generate random location offset for map simulation
 */
export const generateLocationOffset = (
  baseLocation: MapLocation,
  offsetRange: number,
): MapLocation => {
  const randomOffset = () => (Math.random() - 0.5) * offsetRange

  return {
    lat: baseLocation.lat + randomOffset(),
    lng: baseLocation.lng + randomOffset(),
  }
}

/**
 * Format date for API calls
 */
export const formatDateForApi = (dateValue: Date): string => {
  const datePart = dateValue.toISOString().split('T')[0]
  if (!datePart) {
    throw new Error('Invalid date format')
  }
  return datePart
}

/**
 * Create date range for month navigation
 */
export const createMonthDateRange = (currentDate: Date) => {
  const startDate = new Date(currentDate.getFullYear(), currentDate.getMonth(), 1)
  const endDate = new Date(currentDate.getFullYear(), currentDate.getMonth() + 1, 0)

  return { startDate, endDate }
}

// Interface for user filtering
interface FilterableUser {
  id: number | string
  name: string
}

/**
 * Filter users based on search query and filter type
 */
export const filterUsers = <T extends FilterableUser>(
  users: T[],
  searchQuery: string,
  filterType: string,
): T[] => {
  if (!searchQuery) return users

  const searchLower = searchQuery.toLowerCase()

  return users.filter((user) => {
    if (filterType === 'user_id') {
      return user.id.toString().includes(searchLower)
    } else {
      return user.name.toLowerCase().includes(searchLower)
    }
  })
}

/**
 * Debounce function for performance optimization
 */
export const debounce = <T extends (...args: unknown[]) => unknown>(
  func: T,
  wait: number,
): ((...args: Parameters<T>) => void) => {
  let timeout: NodeJS.Timeout | null = null

  return (...args: Parameters<T>) => {
    if (timeout) clearTimeout(timeout)
    timeout = setTimeout(() => func(...args), wait)
  }
}
