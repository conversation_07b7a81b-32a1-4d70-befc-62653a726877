<template>
  <q-toolbar class="bg-color q-pt-md">
    <div class="row full-width no-wrap items-center">
      <!-- ใช้ q-space เพื่อดันปุ่มที่เหลือไปทางขวา -->
      <q-space />

      <!-- ปุ่มเมนู -->
      <div class="col-auto row no-wrap">
        <!-- Manager sees all features -->
        <template v-if="isManager">
          <q-btn
            to="/user/summary"
            unelevated
            label="ภาพรวมพนักงาน"
            class="q-mx-sm"
            :class="{ 'active-btn': isActive === '/user/summary' }"
          />
          <q-btn
            to="/user/management"
            unelevated
            label="จัดการพนักงาน"
            class="q-mx-sm"
            :class="{ 'active-btn': isActive === '/user/management' }"
          />
          <q-btn
            to="/user/checkinout"
            unelevated
            label="เข้า-ออกงาน"
            class="q-mx-sm"
            :class="{ 'active-btn': isActive === '/user/checkinout' }"
          />
        </template>

        <!-- Part-Time and Employee see limited features -->
        <template v-else-if="isPartTimeOrEmployee">
          <q-btn
            to="/user/checkinout"
            unelevated
            label="เข้า-ออกงาน"
            class="q-mx-sm"
            :class="{ 'active-btn': isActive === '/user/checkinout' }"
          />
          <q-btn
            :to="`/user/attendance/${currentUserId}`"
            unelevated
            label="รายงานการทำงาน"
            class="q-mx-sm"
            :class="{ 'active-btn': isActive === `/user/attendance/${currentUserId}` }"
          />
        </template>
      </div>
    </div>
  </q-toolbar>
</template>

<script setup lang="ts">
import { ref, watchEffect, computed } from 'vue'
import { useRoute } from 'vue-router'
import { useAuthStore } from 'src/stores/auth/authStore'

const route = useRoute()
const authStore = useAuthStore()
const isActive = ref(route.path)

watchEffect(() => {
  isActive.value = route.path
})

// Role-based visibility computed properties
const isManager = computed(() => authStore.currentUser?.role === 'Manager')
const isPartTimeOrEmployee = computed(() => {
  const role = authStore.currentUser?.role
  return (
    role === 'Part-time' || // English part-time role
    role === 'Employee' || // English employee role
    role === 'ประจำ' || // Thai employee role
    role === 'พาร์ทไทม์' // Thai part-time role
  )
})
const currentUserId = computed(() => authStore.currentUser?.id)
</script>

<style scoped>
.bg-color {
  background-color: #f3f3f3;
}

.active-btn {
  background-color: #609fa3 !important;
  color: white;
}

/* สีของปุ่มที่ไม่ได้กด */
.q-btn {
  background-color: #b0bec5;
  color: white;
  border-radius: 10px;
  font-weight: bold;
  width: 100%;
  min-width: 141px;
  max-width: 300px;
  box-shadow:
    0 4px 16px 0 rgba(41, 72, 136, 0.1),
    0 1.5px 4px 0 rgba(0, 0, 0, 0.08);
}
</style>
