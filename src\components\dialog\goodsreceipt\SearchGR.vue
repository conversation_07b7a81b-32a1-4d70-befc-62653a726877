<template>
  <q-dialog v-model="isOpen">
    <q-card style="max-width: 1000px; width: 750px">
      <q-card-section>
        <div class="row items-center justify-between">
          <div class="text-h6 text-weight-bold">รายการสั่งซื้อสินค้า</div>
          <q-btn icon="close" @click="closeDialog" flat rounded />
        </div>
      </q-card-section>
      <q-separator />
      <q-card-section style="overflow: auto; max-height: 80vh">
        <div class="gap-container">
          <div class="row q-gutter-sm items-center">
            <div class="col-8">
              <SearchGRDialogComponent v-model="search" placeholder="ค้นหา">
              </SearchGRDialogComponent>
            </div>

            <div>
              <FilterGRDialogComponent v-model="filter" :filterOptions="filterOptions" />
            </div>
          </div>
        </div>

        <!--รายการสินค้า-->
        <div class="gap-container">
          <div class="shadow-2">
            <q-table
              flat
              class="body-table"
              :rows="poStore.orders"
              :columns="columns"
              row-key="id"
              style="height: 300px; width: 100%"
              @row-click="open3"
              :table-row-class-name="getRowClass"
              :pagination="pagination"
              :rows-per-page-options="[]"
              sticky-header
            >
            </q-table>
          </div>
        </div>
      </q-card-section>
      <q-card-actions align="center">
        <q-btn class="btn-accept" dense flat label="บันทึก" color="white" @click="addSelectedPO" />

        <q-btn class="btn-cancel" dense flat label="ยกเลิก" color="white" @click="closeDialog" />
      </q-card-actions>
    </q-card>
  </q-dialog>
</template>
<script setup lang="ts">
import { computed, ref, watch } from 'vue'
import type { QTableColumn } from 'quasar'
import { usePurchaseOrderStore } from 'src/stores/orders/purchaseorder'
import FilterGRDialogComponent from './FilterGRDialogComponent.vue'
import SearchGRDialogComponent from './SearchGRDialogComponent.vue'
import type { PurchaseOrder } from 'src/types/purchaseOrder'
import { useGoodsReceiptStore } from 'src/stores/orders/goodsreceipt'
import { useAuthStore } from 'src/stores/auth/authStore'
import { useUserStore } from 'src/stores/users/userStore'

// Props and emits for v-model support
interface Props {
  modelValue: boolean
  mode?: string
}

const props = defineProps<Props>()
const emit = defineEmits<{
  'update:modelValue': [value: boolean]
  'gr-selected': [grData: PurchaseOrder]
}>()

// Computed property for v-model
const isOpen = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value),
})

const authStore = useAuthStore()
const userStore = useUserStore()
const poStore = usePurchaseOrderStore()
const grStore = useGoodsReceiptStore()
const selectedRowId = ref<number | null>(null)
const search = ref('')
const filter = ref('')
const startDate = ref('')
const endDate = ref('')
const pagination = ref({
  rowsPerPage: 12,
})
watch(
  () => props.modelValue,
  async () => {
    await poStore.fetchOrders()
    await userStore.fetchUsers()
  },
)

// Watch for filter and search changes to refetch data when dialog is open
watch([search, filter, startDate, endDate], async ([search, filter, startDate, endDate]) => {
  if (props.modelValue) {
    await poStore.filterOrders(search, filter, startDate, endDate)
  }
})

const addSelectedPO = () => {
  if (grStore.po) {
    // Emit the selected PO data
    emit('gr-selected', grStore.po)
    const currentUser = computed(() => {
      return authStore.currentUser || userStore.currentUser
    })
    if (props.mode === 'change') {
      grStore.form.po = grStore.po
      grStore.form.distributor = grStore.po.supplier
      grStore.form.date_document = new Date()
      grStore.form.credit_date = new Date()
      grStore.form.tax_invoice_date = new Date()
      grStore.form.po_code = grStore.po.code
      grStore.form.tax = grStore.po.tax
      grStore.form.tax_total = grStore.po.tax_total
      grStore.form.vat_percent = grStore.po.vat_percent
      grStore.form.credit_days = grStore.po.supplier.credit_days
      grStore.form.is_tax_invoice = true
      grStore.form.product_price_tax = grStore.po.product_price_tax
      if (grStore.po.order_discount_tax === 'ก่อนภาษี') {
        grStore.form.is_manual_discount_before_tax = true //ส้วนลดท้ายบิลก่อนภาษี
        grStore.form.is_manual_discount_after_tax = true //ส้วนลดท้าย
        grStore.form.order_discount_tax = grStore.po.product_price_tax
      } else {
        grStore.form.is_manual_discount_after_tax = true //ส้วนลดท้าย
      }
      grStore.form.user = userStore.users.find((user) => user.id === currentUser.value?.id) ?? {
        id: 0,
        name: '',
        password: '',
        tel: '',
        role: '',
        hour_work: 0,
        sick_level: 0,
        personal_leave: 0,
        sick_leave_remaining: 0,
        personal_leave_remaining: 0,
        image: '',
        day_off: '',
        branch: {
          id: 0,
          name: '',
          address: '',
          contact_name: '',
          contact_phone: '',
        },
      }
      grStore.form.is_before_tax_discount = '% ส่วนลด'
      grStore.form.is_after_tax_discount = '% ส่วนลด'
      grStore.form.branch = currentUser.value?.branch ?? {
        id: 0,
        name: '',
        address: '',
        contact_name: '',
        contact_phone: '',
      }
      grStore.form.gr_total = 0.0
      closeDialog()
      return
    }
    // Set form data

    grStore.resetForm()
    grStore.form.po = grStore.po
    grStore.form.distributor = grStore.po.supplier
    grStore.form.date_document = new Date()
    grStore.form.credit_date = new Date()
    grStore.form.tax_invoice_date = new Date()
    grStore.form.po_code = grStore.po.code
    grStore.form.tax = grStore.po.tax
    grStore.form.tax_total = grStore.po.tax_total
    grStore.form.vat_percent = grStore.po.vat_percent
    grStore.form.credit_days = grStore.po.supplier.credit_days
    grStore.form.is_tax_invoice = true
    grStore.form.product_price_tax = grStore.po.product_price_tax
    if (grStore.po.order_discount_tax === 'ก่อนภาษี') {
      grStore.form.is_manual_discount_before_tax = true //ส้วนลดท้ายบิลก่อนภาษี
      grStore.form.is_manual_discount_after_tax = true //ส้วนลดท้าย
      grStore.form.order_discount_tax = grStore.po.product_price_tax
    } else {
      grStore.form.is_manual_discount_after_tax = true //ส้วนลดท้าย
    }
    grStore.form.user = userStore.users.find((user) => user.id === currentUser.value?.id) ?? {
      id: 0,
      name: '',
      password: '',
      tel: '',
      role: '',
      hour_work: 0,
      sick_level: 0,
      personal_leave: 0,
      sick_leave_remaining: 0,
      personal_leave_remaining: 0,
      image: '',
      day_off: '',
      branch: {
        id: 0,
        name: '',
        address: '',
        contact_name: '',
        contact_phone: '',
      },
    }
    grStore.form.is_before_tax_discount = '% ส่วนลด'
    grStore.form.is_after_tax_discount = '% ส่วนลด'
    grStore.form.branch = currentUser.value?.branch ?? {
      id: 0,
      name: '',
      address: '',
      contact_name: '',
      contact_phone: '',
    }
    grStore.form.gr_total = 0.0
  }
  closeDialog()
}

const filterOptions = ref([
  { label: 'เลขที่สั่งซื้อ', value: 'code' },
  { label: 'รหัสบริษัทจำหน่าย', value: 'supplier.supplier_number' },
  { label: 'ชื่อบริษัทจำหน่าย', value: 'supplier.name' },
])

const columns = <QTableColumn[]>[
  {
    name: 'code',
    label: 'เลขที่',
    field: 'code',
    align: 'left' as const,
    sortable: true,
  },
  {
    name: 'date',
    label: 'วันที่',
    field: (row) => {
      const date = new Date(row.date)
      const year = date.getFullYear()
      const month = String(date.getMonth() + 1).padStart(2, '0') // เดือนต้อง +1 เพราะเริ่มจาก 0
      const day = String(date.getDate()).padStart(2, '0')
      return `${year}/${month}/${day}`
    },
    align: 'left' as const,
    sortable: true,
  },
  {
    name: 'supplier',
    label: 'บริษัท',
    field: (row) => (row.supplier ? `${row.supplier.supplier_number} ${row.supplier.name}` : ''),
    align: 'left' as const,
  },
  {
    name: 'po_total',
    label: 'ราคา',
    field: 'po_total',
    align: 'left' as const,
  },
]
function getRowClass(row: PurchaseOrder) {
  return row.id === selectedRowId.value ? 'selected-row' : ''
}
function open3(_evt: Event, row: PurchaseOrder) {
  // console.log('🚀 Clicked Row Object:', row)
  grStore.po = row
  selectedRowId.value = row.id
}

const closeDialog = () => {
  // dialogBtn.closePO()
  isOpen.value = false
}
</script>
<style scoped>
.selected-row {
  background-color: #36b54d !important;
  /* สีฟ้าอ่อน */
}

.container {
  background-color: #deecff;
  padding: 16px;
  border-bottom-left-radius: 10px;
  border-bottom-right-radius: 10px;
  border-top-left-radius: 0px;
  border-top-right-radius: 0px;
}

.container-header {
  background-color: #294888;
  padding-left: 16px;
  border-top-left-radius: 10px;
  border-top-right-radius: 10px;
  height: 55px;
}

.mini-container {
  padding-left: 10px;
  padding-right: 10px;
  background-color: white;
  border-radius: 5px;
}

.input-container {
  padding-left: 10px;
  padding-right: 10px;
  background-color: white;
  margin-top: 10px;
  margin-bottom: 10px;
  border-radius: 5px;
}

.btn-accept {
  background-color: #36b54d;
  margin-right: 10px;
}

.btn-cancel {
  background-color: #9c2c2c;
  margin-right: 10px;
}

.gap-container {
  margin-bottom: 20px;
}

.dialog-container {
  border-radius: 10px;
}

.container-search {
  display: flex;
  align-items: center;
  padding-left: 10px;
  padding-right: 10px;
  margin-top: 10px;
  margin-bottom: 5px;
  border-radius: 5px;
  background-color: #294888;
  width: 400px;
  height: 50px;
}

.input-container-search {
  padding-left: 10px;
  padding-right: 10px;
  background-color: white;
  margin-top: 5px;
  margin-bottom: 5px;
  border-radius: 5px;
  height: 35px;
  width: 250px;
}

.column-table {
  height: 40px;
  background-color: #294888;
  color: white;
}

.radio-container {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  margin-left: 20px;
}

.q-radio {
  margin-right: 15px;
}

.custom-table {
  border-radius: 10px;
  overflow: hidden;
}

:deep(.q-table thead tr) {
  background-color: #294888;
  color: #deecff;
}

/* Ensure sticky header maintains styling */
:deep(.q-table--sticky-header thead th) {
  background-color: #294888;
  color: #deecff;
  position: sticky;
  z-index: 1;
}

.body-table {
  background-color: #deecff;
}
</style>
