<template>
  <q-dialog v-model="isOpen">
    <q-card style="max-width: 1100px; width: 800px">
      <q-card-section>
        <div class="row items-center justify-between">
          <div class="text-h6 text-weight-bold">ค้นหาสินค้า</div>
          <q-btn icon="close" @click="closeDialog" flat rounded />
        </div>
      </q-card-section>
      <q-separator />
      <q-card-section style="overflow: auto; max-height: 80vh">
        <div class="gap-container">
          <div class="row items-center q-gutter-md">
            <div class="search-container">
              <SearchProductDialogComponent
                v-model="stockStore.searchTextDialog"
                placeholder="ค้นหา"
              />
            </div>
            <div class="filter-container">
              <FilterProductDialogComponent
                v-model="stockStore.selectedFilterDialog"
                :filterOptions="filterOptions"
              >
              </FilterProductDialogComponent>
            </div>
          </div>
        </div>
        <!--รายการสินค้า-->
        <div class="gap-container">
          <div class="shadow-2">
            <q-table
              flat
              class="body-table"
              :rows="stockStore.stocksDialog"
              :columns="columns"
              row-key="id"
              style="height: 380px"
              @row-click="open3"
              :pagination="pagination"
              :rows-per-page-options="[]"
            >
              <template v-slot:body-cell-index="props">
                <q-td :props="props">
                  {{ props.rowIndex + 1 }}
                </q-td>
              </template>
            </q-table>
          </div>
        </div>
      </q-card-section>
      <q-card-actions align="center">
        <q-btn
          class="btn-accept"
          dense
          flat
          label="บันทึก"
          color="white"
          @click="addSelectedProducts"
        />

        <q-btn class="btn-cancel" dense flat label="ยกเลิก" color="white" @click="closeDialog" />
      </q-card-actions>
    </q-card>
  </q-dialog>
  <EditSTSItem v-model="editSTOItemDialogOpen" :mode="modeEditSTOItem"></EditSTSItem>
</template>
<script setup lang="ts">
import SearchProductDialogComponent from 'src/components/searchProductDialogComponent.vue'
import { useStockStore } from 'src/stores/inventory/stock'
import { ref, watch, computed } from 'vue'
import FilterProductDialogComponent from 'src/components/filterProductDialogComponent.vue'
import { useBranchStore } from 'src/stores/inventory/branch'
import type { QTableColumn } from 'quasar'
import type { Stock } from 'src/types/stock'
import { useStockTransferOrderStore } from 'src/stores/orders/stocktransferorder'
import EditSTSItem from './EditSTSItem.vue'

// Props and emits for v-model support
interface Props {
  modelValue: boolean
}

const props = defineProps<Props>()
const emit = defineEmits<{
  'update:modelValue': [value: boolean]
  'product-added': []
}>()

// Computed property for v-model
const isOpen = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value),
})

const stockStore = useStockStore()
const stoStore = useStockTransferOrderStore()
const storeBranch = useBranchStore()
const pagination = ref({
  rowsPerPage: 12,
})
const editSTOItemDialogOpen = ref(false)
const modeEditSTOItem = ref('add')

const addSelectedProducts = () => {
  // Open ReceivePDDialog instead of processing items
  const newItems = stoStore.stoDetailsEdited.filter((item) => {
    const existsInOrder = stoStore.stoDetails.some(
      (existing) => existing.product.id === item.product.id,
    )
    const isDeleted = stoStore.deletedIds.includes(item.product.id)

    return isDeleted || !existsInOrder // ถ้าเคยลบให้เพิ่มใหม่ หรือยังไม่มีอยู่
  })

  if (newItems.length > 0) {
    // 2️⃣ เพิ่มสินค้าใหม่เข้าไป โดยไม่ทำให้ของเก่าหาย
    newItems.forEach((item) => {
      const existingItemIndex = stoStore.stoDetails.findIndex(
        (existing) => existing.product.id === item.product.id,
      )
      if (existingItemIndex !== -1) {
        // ถ้ามีอยู่แล้ว ให้อัปเดตค่าแทน
        stoStore.stoDetails[existingItemIndex] = {
          ...stoStore.stoDetails[existingItemIndex],
          ...item,
        }
      } else {
        // ถ้ายังไม่มี ให้เพิ่มใหม่
        stoStore.stoDetails.push({ ...item })
      }
    })

    // 3️⃣ เอา product.id ออกจาก deletedIds ถ้ามีการเพิ่มกลับมา
    stoStore.deletedIds = stoStore.deletedIds.filter(
      (id) => !newItems.some((item) => item.product.id === id),
    )
  }

  // Close the current dialog
  closeDialog()
}

// Watch for dialog opening to fetch initial data
watch(
  () => props.modelValue,
  async (newValue) => {
    if (newValue) {
      // Fetch products by distributor if distributor is selected
      // stockStore.resetStocksDialog()
      if (stoStore.form.source_branch && stoStore.form.source_branch.id) {
        stockStore.selectedBranchDialog = String(stoStore.form.source_branch?.id) || ''
        await stockStore.fetchAllStockByFilterDialog()
      }
      await storeBranch.fetchAllBranch()
      stoStore.stoDetailsEdited = stoStore.stoDetails
    }
  },
)

watch([() => stockStore.searchTextDialog, () => stockStore.selectedFilterDialog], async () => {
  await stockStore.fetchAllStockByFilterDialog()
})

const filterOptions = ref([
  { label: 'รหัสสินค้า', value: 'product_code' },
  { label: 'ชื่อสินค้า', value: 'product_name' },
  { label: 'กลุ่มชื่อสามัญ', value: 'generic_group' },
  { label: 'บาร์โค้ด', value: 'barcode' },
  // { label: 'สถานที่เก็บ', value: 'storage_location' },
])

const columns = <QTableColumn[]>[
  {
    name: 'index',
    label: 'ลำดับ',
    field: 'index',
    align: 'left' as const,
    sortable: true,
  },
  {
    name: 'product_code',
    label: 'รหัสสินค้า',
    field: (row) => (row.product ? row.product.product_code : '-'),
    align: 'left' as const,
    sortable: true,
  },
  {
    name: 'product_name',
    label: 'ชื่อสินค้า',
    field: (row) => (row.product ? row.product.product_name : '-'),
    align: 'left' as const,
  },
  {
    name: 'unit',
    label: 'หน่วย',
    field: (row) => (row.product ? row.product.unit : '-'),
    align: 'left' as const,
  },
  {
    name: 'remaining',
    label: 'คงเหลือ',
    field: (row) => row.remaining,
    align: 'left' as const,
  },
  {
    name: 'selling_price',
    label: 'ราคาขาย',
    field: (row) => (row.product ? row.product.selling_price : '-'),
    align: 'left' as const,
  },
]

async function open3(_evt: Event, row: Stock) {
  console.log('🚀 Clicked Row Object:', row)
  stockStore.resetFormForGR()
  await stockStore.fetchStockByProductId(row.product.id)
  stoStore.formSTODetails.id = 0
  stoStore.formSTODetails.product = row.product
  stoStore.formSTODetails.quantity = 1

  editSTOItemDialogOpen.value = true
  modeEditSTOItem.value = 'add'
}

const closeDialog = () => {
  stockStore.resetStocksDialog()
  isOpen.value = false
}
</script>
<style scoped>
.container {
  background-color: #deecff;
  padding: 16px;
  border-bottom-left-radius: 10px;
  border-bottom-right-radius: 10px;
  border-top-left-radius: 0px;
  border-top-right-radius: 0px;
}

.container-header {
  background-color: #294888;
  padding-left: 16px;
  border-top-left-radius: 10px;
  border-top-right-radius: 10px;
  height: 55px;
}

.mini-container {
  padding-left: 10px;
  padding-right: 10px;
  background-color: white;
  border-radius: 5px;
}

.input-container {
  padding-left: 10px;
  padding-right: 10px;
  background-color: white;
  margin-top: 10px;
  margin-bottom: 10px;
  border-radius: 5px;
}

.btn-accept {
  background-color: #36b54d;
  margin-right: 10px;
}

.btn-cancel {
  background-color: #9c2c2c;
  margin-right: 10px;
}

.gap-container {
  margin-bottom: 20px;
}

.dialog-container {
  border-radius: 10px;
}

.column-table {
  height: 40px;
  background-color: #294888;
  color: white;
}

.radio-container {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  margin-left: 20px;
}

.q-radio {
  margin-right: 15px;
}

.custom-table {
  border-radius: 10px;
  overflow: hidden;
}

:deep(.q-table thead tr) {
  background-color: #294888;
  color: #deecff;
}

.body-table {
  background-color: #deecff;
}

.search-container {
  flex: 1;
  display: flex;
  align-items: center;
}

.filter-container {
  flex-shrink: 0;
  display: flex;
  align-items: center;
}
</style>
