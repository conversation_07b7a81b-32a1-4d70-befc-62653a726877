import { api } from 'src/boot/axios'
import type { StockTransferOrderDetails } from 'src/types/stockTransferOrderDetails'

export class StockTransferOrderDetailsService {

  static path = 'sto-details'

  static async getAll(): Promise<StockTransferOrderDetails[]> {
    const response = await api.get(this.path)
    return response.data
  }
  static async getById(id: number): Promise<StockTransferOrderDetails> {
    const response = await api.get(`${this.path}/${id}`)
    return response.data
  }
  static async getDetailsBySTO(id: number): Promise<StockTransferOrderDetails[]> {
    const response = await api.get(`${this.path}/sto/${id}`)
    return response.data
  }

  static async create(stoDetails: StockTransferOrderDetails[], stoId: number) {
    await api.post(`${this.path}/${stoId}`, stoDetails)
  }

  // อัปเดต Purchase Order
  static async update(id: number, gr: Partial<StockTransferOrderDetails>): Promise<StockTransferOrderDetails> {
    const response = await api.put(`${this.path}/${id}`, gr)
    return response.data
  }

  static async updateGRDetailsFromPO(grDetails: StockTransferOrderDetails[], grId: number) {
    await api.put(`${this.path}/${grId}`, grDetails)
  }

  // ลบ Purchase Order
  static async delete(id: number): Promise<void> {
    await api.delete(`${this.path}/${id}`)
  }

  static async filter(
    search: string,
    filter: string,
    startDate: string,
    endDate: string,
  ): Promise<StockTransferOrderDetails[]> {
    try {
      const response = await api.post(`${this.path}/filter`, {
        search,
        filter,
        startDate,
        endDate,
      })
      return response.data
    } catch (error) {
      console.error('Error filtering purchase orders', error)
      throw error
    }
  }
}
