import type { Branch } from "./branch";
import type { StockTransferOrderDetails } from "./stockTransferOrderDetails";
import type { StockTransferSlip } from "./stockTransferSlip";
import type { user } from "./user";

export interface StockTransferOrder {
  id: number;
  sts: StockTransferSlip;
  code: string;
  source_branch: Branch;
  destination_branch: Branch;
  request_date: Date;
  user: user;
  note: string;
  status: string;
  transfer_status: string;
  sto_price?: number;
  sto_details: StockTransferOrderDetails;
}

