<template>
  <div class="filter-box">
    <q-select
      v-model="selectedFilter"
      :options="filterOptions"
      label="ตัวกรอง"
      borderless
      dense
      options-dense
      behavior="menu"
      emit-value
      map-options
      popup-content-class="custom-dropdown"
    >
      <template v-slot:selected>
        <span>{{ selectedFilterLabel }}</span>
      </template>
      <template v-slot:option="scope">
        <q-item clickable v-ripple @click="toggleFilter(scope.opt.value)">
          <q-item-section avatar>
            <q-radio v-model="selectedFilter" :val="scope.opt.value" color="black" />
          </q-item-section>
          <q-item-section>
            {{ scope.opt.label }}
          </q-item-section>
        </q-item>
      </template>
    </q-select>
  </div>
</template>

<script setup lang="ts">
import { defineProps, defineEmits, ref, computed, watch } from 'vue'

interface FilterOption {
  label: string
  value: string
}

const props = defineProps({
  modelValue: String,
  filterOptions: {
    type: Array as () => FilterOption[],
    required: true,
  },
})

const emit = defineEmits(['update:modelValue'])

// แก้ไข: ให้ selectedFilter เป็น string เสมอ
const selectedFilter = ref<string>(props.modelValue || '')

const toggleFilter = (value: string) => {
  selectedFilter.value = selectedFilter.value === value ? '' : value
  emit('update:modelValue', selectedFilter.value)
}

watch(
  () => props.modelValue,
  (newValue) => {
    selectedFilter.value = newValue || ''
  },
)

const selectedFilterLabel = computed(() => {
  return props.filterOptions.find((opt) => opt.value === selectedFilter.value)?.label || ''
})
</script>

<style scoped>
.filter-box {
  background-color: #c3e7dd;
  padding-left: 10px;
  padding-right: 10px;
  border-radius: 5px;
  width: 100%;
  max-width: 310px;
}

.custom-dropdown .q-item {
  background-color: #e1edea !important;
  color: black !important;
}
</style>
