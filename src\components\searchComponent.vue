<template>
  <q-input v-model="searchTerm" :placeholder="placeholder" borderless dense class="search-box">
    <template v-slot:append>
      <q-icon name="search" />
    </template>
  </q-input>
</template>

<script setup lang="ts">
import { defineProps, defineEmits, ref, watch } from 'vue'

const props = defineProps({
  modelValue: String,
  placeholder: {
    type: String,
    default: 'ค้นหา',
  },
})

const emit = defineEmits(['update:modelValue'])
const searchTerm = ref(props.modelValue)

watch(searchTerm, (newValue) => {
  emit('update:modelValue', newValue)
})
</script>

<style scoped>
.search-box {
  background-color: #c3e7dd;
  padding-left: 10px;
  padding-right: 10px;
  border-radius: 5px;
  width: 100%;
}
</style>
