<!--
  TimeDisplayCard Component
  Displays current time and date in a card format
-->
<template>
  <q-card flat :class="props.cardClass">
    <q-card-section class="text-center">
      <div class="time-display">{{ props.currentTime }}</div>
      <div class="date-display">{{ props.currentDate }}</div>
    </q-card-section>
  </q-card>
</template>

<script setup lang="ts">
interface Props {
  currentTime: string
  currentDate: string
  cardClass?: string
}

const props = withDefaults(defineProps<Props>(), {
  cardClass: 'time-card',
})
</script>

<style scoped>
.time-display {
  font-size: 4rem;
  font-weight: bold;
  line-height: 1;
}

.date-display {
  font-size: 1.2rem;
  margin-top: 8px;
  opacity: 0.8;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .time-display {
    font-size: 3rem;
  }

  .date-display {
    font-size: 1rem;
  }
}

@media (max-width: 480px) {
  .time-display {
    font-size: 2.5rem;
  }

  .date-display {
    font-size: 0.9rem;
  }
}
</style>
