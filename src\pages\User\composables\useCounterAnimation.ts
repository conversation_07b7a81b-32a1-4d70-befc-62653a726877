import { ref, watch, type Ref } from 'vue'

export function useCounterAnimation(
  targetValue: Ref<number>,
  duration: number = 2000,
  decimals: number = 0,
) {
  const animatedValue = ref(0)
  const isAnimating = ref(false)

  const startAnimation = (target: number) => {
    if (isAnimating.value) return

    isAnimating.value = true
    const startValue = animatedValue.value
    const endValue = target
    const startTime = performance.now()

    const animate = (currentTime: number) => {
      const elapsedTime = currentTime - startTime
      const progress = Math.min(elapsedTime / duration, 1)

      // Easing function (easeOutQuart)
      const easedProgress = 1 - Math.pow(1 - progress, 4)

      // Calculate current value
      const currentValue = startValue + (endValue - startValue) * easedProgress
      animatedValue.value = Number(currentValue.toFixed(decimals))

      if (progress < 1) {
        requestAnimationFrame(animate)
      } else {
        animatedValue.value = endValue
        isAnimating.value = false
      }
    }

    requestAnimationFrame(animate)
  }

  // Watch for changes in target value to trigger animation
  watch(
    targetValue,
    (newValue) => {
      if (newValue !== animatedValue.value) {
        startAnimation(newValue)
      }
    },
    { immediate: true },
  )

  return {
    animatedValue,
    isAnimating,
  }
}
