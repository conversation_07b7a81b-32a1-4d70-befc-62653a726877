<template>
  <q-dialog v-model="isOpen">
    <q-card style="max-width: 1100px; width: 500px">
      <q-card-section>
        <div class="row items-center justify-between">
          <div class="text-h6 text-weight-bold">รายละเอียดสินค้า</div>
          <q-btn icon="close" @click="closeDialog" flat rounded />
        </div>
      </q-card-section>
      <q-separator />
      <q-card-section style="overflow: auto; max-height: 80vh">
        <div class="gap-container">
          <div class="text-white shadow-2 container-header row items-center">
            รายละเอียดการสั่งสินค้า
          </div>
          <div class="shadow-2 container">
            <div class="q-mb-md" style="font-size: 16px">
              <div class="row items-center q-mt-md">
                <div class="col-4">รหัสสินค้า :</div>
                <div class="col-8">{{ stockStore.formforGR.product.product_code }}</div>
              </div>
              <div class="row items-center q-mt-md" style="margin-top: 20px">
                <div class="col-4">ชื่อสินค้า :</div>
                <div class="col-8">{{ stockStore.formforGR.product.product_name }}</div>
              </div>
              <div class="row items-center q-mt-md">
                <div class="col-4">จำนวนคงเหลือ :</div>
                <div class="col-8">
                  {{ stockStore.formforGR.remaining }}
                  <span>{{ stockStore.formforGR.product.unit }}</span>
                  <q-btn
                    label="ดูจำนวนทุกสาขา"
                    flat
                    size="md"
                    class="btn-add"
                    @click="viewStockByBranchDialogOpen = true"
                  />
                </div>
              </div>
              <div class="row items-center q-mt-md">
                <div class="col-4">สถานที่เก็บ :</div>
                <div class="col-8">{{ stockStore.formforGR.product.storage_location }}</div>
              </div>
              <div class="row items-center q-mt-md">
                <div class="col-4">จำนวนการสั่งสินค้า :</div>
                <div class="col-8">
                  <q-input
                    class="input-container"
                    dense
                    type="number"
                    v-model.number="stoStore.formSTODetails.quantity"
                    ref="quantityInput"
                    @focus="quantityInput?.getNativeElement()?.select()"
                    style="max-width: 150px"
                  />
                </div>
              </div>
              <!-- <div class="row justify-center q-mt-sm"> -->

              <!-- </div> -->
            </div>
          </div>
        </div>
      </q-card-section>
      <q-card-actions align="center" style="color: white">
        <q-btn class="btn-accept" dense flat label="บันทึก" @click="saveDialog" />
        <q-btn class="btn-cancel" dense flat label="ยกเลิก" @click="closeDialog" />
      </q-card-actions>
    </q-card>
  </q-dialog>
  <ViewStockByBranch v-model="viewStockByBranchDialogOpen"></ViewStockByBranch>
</template>

<script setup lang="ts">
import { useStockStore } from 'src/stores/inventory/stock'
import { useStockTransferOrderStore } from 'src/stores/orders/stocktransferorder'
import type { StockTransferOrderDetails } from 'src/types/stockTransferOrderDetails'
import { computed, ref, watch } from 'vue'
import ViewStockByBranch from './ViewStockByBranch.vue'
import { QInput } from 'quasar'

// Props and emits for v-model support
interface Props {
  modelValue: boolean
  stoItem?: StockTransferOrderDetails | null
  mode?: string
}

const props = withDefaults(defineProps<Props>(), {
  stoItem: null,
  mode: 'add',
})

const emit = defineEmits<{
  'update:modelValue': [value: boolean]
  'item-updated': []
}>()

// Computed property for v-model
const isOpen = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value),
})

const stockStore = useStockStore()
const stoStore = useStockTransferOrderStore()
const viewStockByBranchDialogOpen = ref(false)
const quantityInput = ref<InstanceType<typeof QInput> | null>(null)
watch(
  () => props.modelValue,
  () => {
    const inputEl = quantityInput.value?.getNativeElement?.()
    if (inputEl) {
      inputEl.focus()
      inputEl.select()
    }
  },
)

const saveDialog = () => {
  const index = stoStore.stoDetails.findIndex(
    (item) => item.product.id === stoStore.formSTODetails.product.id,
  )

  if (props.mode === 'edit') {
    if (index !== -1) {
      stoStore.stoDetails[index] = {
        ...stoStore.stoDetails[index],
        ...stoStore.formSTODetails,
      }
    } else {
      stoStore.stoDetails.push(stoStore.formSTODetails)
    }
    stoStore.deletedIds = stoStore.deletedIds.filter(
      (id) => id !== stoStore.formSTODetails.product.id,
    )
  } else {
    const exists = stoStore.stoDetailsEdited.some(
      (item) => item.product.id === stoStore.formSTODetails.product.id,
    )
    if (!exists) {
      stoStore.stoDetailsEdited.push(stoStore.formSTODetails)
    }
    // else{
    //   store.grDetailsEdited[index] = {
    //     ...originalItem,
    //     ...copiedFormOrderItems,
    //     id: oldId, // ป้องกัน id หาย
    //     quantity: updatedQuantity
    //   };
    // }
  }

  isOpen.value = false
  stoStore.resetFormSTODetails()
}

const closeDialog = () => {
  if (props.stoItem) {
    // หา item ที่เพิ่งบันทึก แล้วอัปเดตกลับเข้า formOrderItems
    const editedItem = stoStore.stoDetails.find(
      (item) => item.product.id === stoStore.formSTODetails.product.id,
    )
    if (editedItem) {
      Object.assign(stoStore.formSTODetails, editedItem)
    }
  } else {
    stoStore.resetFormSTODetails()
  }
  isOpen.value = false
}
</script>

<style scoped>
:deep(.q-table thead tr) {
  font-size: 12pt;
  background-color: #83a7d8;
}

.row-table {
  align-items: center;
  height: 40px;
  background-color: #83a7d8;
  border-top-left-radius: 10px;
  border-top-right-radius: 10px;
  display: grid;
  grid-template-columns: 1fr 1fr 1fr;
  /* กำหนด 3 คอลัมน์ที่มีขนาดเท่ากัน */
  align-items: center;
  text-align: center;
}

.row-table-body {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr;
  /* กำหนด 3 คอลัมน์ที่มีขนาดเท่ากัน */
  align-items: center;
  text-align: center;
}

.col-3 {
  padding: 8px;
}

.row-table .col-3 {
  flex: 1;
  text-align: center;
}

.custom-table {
  border-radius: 10px;
  overflow: hidden;
}

.body-table {
  background-color: #ffffff;
}

.status-container {
  flex-direction: column;
  align-items: center;
  height: 40px;
  width: 165px;
  text-align: center;
  font-family: sans-serif;
}

.status-header {
  background-color: #83a7d8;
  padding: 10px;
  color: white;
  height: 40px;
  width: 170px;
  border-top-left-radius: 10px;
  border-top-right-radius: 10px;
}

.status-body {
  background-color: #fff;
  padding: 10px;
  height: 40px;
  width: 170px;

  border-bottom-left-radius: 10px;
  border-bottom-right-radius: 10px;
}

.container {
  background-color: #deecff;
  padding: 16px;
  border-bottom-left-radius: 10px;
  border-bottom-right-radius: 10px;
  border-top-left-radius: 0px;
  border-top-right-radius: 0px;
}

.header-box {
  background-color: #83a7d8;
  border-top-left-radius: 10px;
  border-top-right-radius: 10px;
  height: 40px;
  width: 160px;
  text-align: center;
}

.showDetail-Box {
  background-color: #ffffff;
  border-bottom-left-radius: 10px;
  border-bottom-right-radius: 10px;
  height: 40px;
  width: 160px;
  text-align: center;
}

.header-box2 {
  background-color: #83a7d8;
  border-top-left-radius: 10px;
  border-top-right-radius: 10px;
  height: 40px;
  width: 160px;
  text-align: center;
}

.container-header {
  background-color: #294888;
  padding-left: 16px;
  border-top-left-radius: 10px;
  border-top-right-radius: 10px;
  height: 55px;
}

.mini-container {
  padding-left: 10px;
  padding-right: 10px;
  background-color: white;
  border-radius: 5px;
}

.input-container {
  padding-left: 10px;
  padding-right: 10px;
  background-color: white;
  border-radius: 5px;
}

.btn-accept {
  background-color: #36b54d;
  margin-right: 10px;
}

.btn-cancel {
  background-color: #9c2c2c;
  margin-right: 10px;
}

.gap-container {
  margin-bottom: 20px;
}

.dialog-container {
  border-radius: 10px;
}

.input-container-v2 {
  padding-left: 10px;
  padding-right: 10px;
  border-radius: 5px;
  background-color: #83a7d8;
}

.input-container-v3 {
  padding-left: 10px;
  padding-right: 10px;
  margin-top: 5px;
  margin-bottom: 5px;
  border-radius: 5px;
  background-color: #d6e4f6;
  width: 150px;
}

.width-column {
  width: 300px;
}

.column-table {
  height: 40px;
  background-color: #294888;
  color: white;
}

.column-table-2 {
  height: 40px;
  padding-left: 10px;
  padding-right: 10px;
  background-color: white;
  border: 2px solid #294888;
}

.quantity-box {
  display: flex;
  align-items: center;
  align-items: center;
  justify-content: center;
  border: 2px solid rgb(201, 201, 201);
  border-radius: 5px;
  padding: 3px;
  background-color: white;
  width: 150px;
  height: 40px;
  margin-left: 40px;
}

.quantity-btn {
  margin-top: 0%;
  margin-bottom: 0%;
  width: 50px;
  height: 35px;
  background-color: white !important;
  color: black !important;
  border-radius: 0%;
  border-color: grey;
  border-right: 2px solid #c9c9c9;
  align-items: center;
  justify-content: center;
}

.quantity-btn2 {
  /* margin-left: 10px;
  margin-right: 10px; */
  margin-top: 0%;
  margin-bottom: 0%;
  width: 50px;
  height: 35px;
  background-color: white !important;
  color: black !important;
  border-radius: 0%;
  border-left: 2px solid #c9c9c9;
  align-items: center;
  justify-content: center;
}

.quantity-input {
  margin-bottom: 0%;
  margin-top: 0%;
  margin-left: 55px;
  width: 50px;
  text-align: center;
  color: black;
  font-weight: bold;
  border: none;
  background: transparent;
}

.quantity-input2 {
  margin-bottom: 0%;
  margin-top: 0%;
  /* margin-left: 10px; */
  width: 50px;
  text-align: center;
  color: black;
  font-weight: bold;
  border: none;
  background: transparent;
}

.total-text {
  margin-left: 52px;
}

/* .quantity-btn,
.quantity-btn2 {
  min-width: 32px;
  height: 32px;
}

.quantity-input {
  text-align: center;
} */

.order-list {
  border-radius: 8px;
  /* padding: 8px; */
  border-top-left-radius: 0px;
  border-top-right-radius: 0px;
  background: white;
}

.btn-add {
  background-color: #294888;
  color: white;
  border-radius: 5px;
  margin-left: 20px;
  font-size: 13px;
}
</style>
