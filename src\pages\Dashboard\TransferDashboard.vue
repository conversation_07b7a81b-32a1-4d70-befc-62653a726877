<template>
  <q-page class="q-pa-md">
    <div class="text-h4 q-mb-md">🔄 Transfer/Issue Dashboard</div>

    <div class="row q-gutter-md">
      <!-- Volume by Branch Chart -->
      <div class="col-12 col-md-6">
        <q-card class="full-height">
          <q-card-section>
            <div class="text-h6">Transfer Volume by Branch</div>
            <canvas ref="branchVolumeChartRef"></canvas>
          </q-card-section>
        </q-card>
      </div>

      <!-- Top Issued Products Chart -->
      <div class="col-12 col-md-6">
        <q-card class="full-height">
          <q-card-section>
            <div class="text-h6">Top Issued Products</div>
            <canvas ref="topProductsChartRef"></canvas>
          </q-card-section>
        </q-card>
      </div>

      <!-- Transfer Flow Map -->
      <div class="col-12">
        <q-card>
          <q-card-section>
            <div class="text-h6">Transfer Flow Map</div>
            <canvas ref="flowMapChartRef"></canvas>
          </q-card-section>
        </q-card>
      </div>
    </div>
  </q-page>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { Chart, registerables } from 'chart.js'
import type { TooltipItem } from 'chart.js'
import { api } from 'src/boot/axios'

Chart.register(...registerables)

const branchVolumeChartRef = ref<HTMLCanvasElement>()
const topProductsChartRef = ref<HTMLCanvasElement>()
const flowMapChartRef = ref<HTMLCanvasElement>()

// --- Interfaces ---
interface TransferVolume {
  source_branch: string
  destination_branch: string
  total_transferred: number
}

interface IssuedProduct {
  product_name: string
  total_transferred: number
}

interface TransferFlow {
  source_name: string
  destination_name: string
  flow_quantity: number
}

// --- Chart Creators ---
const createBranchVolumeChart = async () => {
  try {
    const response = await api.get<TransferVolume[]>('/dashboard/transfer/volume-by-branch')
    const data = response.data

    new Chart(branchVolumeChartRef.value!, {
      type: 'bar',
      data: {
        labels: data.map((item) => `${item.source_branch} → ${item.destination_branch}`),
        datasets: [
          {
            label: 'Total Transferred',
            data: data.map((item) => item.total_transferred),
            backgroundColor: '#FF9800',
          },
        ],
      },
      options: {
        responsive: true,
        indexAxis: 'y',
        scales: {
          x: { beginAtZero: true },
        },
      },
    })
  } catch (error) {
    console.error('Error creating branch volume chart:', error)
  }
}

const createTopProductsChart = async () => {
  try {
    const response = await api.get<IssuedProduct[]>('/dashboard/transfer/top-issued-products')
    const data = response.data.slice(0, 10)

    new Chart(topProductsChartRef.value!, {
      type: 'doughnut',
      data: {
        labels: data.map((item) => item.product_name),
        datasets: [
          {
            data: data.map((item) => item.total_transferred),
            backgroundColor: [
              '#FF9800',
              '#4CAF50',
              '#2196F3',
              '#9C27B0',
              '#F44336',
              '#607D8B',
              '#795548',
              '#E91E63',
              '#26A69A',
              '#AB47BC',
            ],
          },
        ],
      },
      options: {
        responsive: true,
        plugins: {
          legend: { position: 'bottom' },
        },
      },
    })
  } catch (error) {
    console.error('Error creating top products chart:', error)
  }
}

const createFlowMapChart = async () => {
  try {
    const response = await api.get<TransferFlow[]>('/dashboard/transfer/flow-map')
    const data = response.data

    new Chart(flowMapChartRef.value!, {
      type: 'bubble',
      data: {
        datasets: [
          {
            label: 'Transfer Flow',
            data: data.map((item, index) => ({
              x: index % 5,
              y: Math.floor(index / 5),
              r: Math.sqrt(item.flow_quantity) / 10,
              label: `${item.source_name} → ${item.destination_name}`,
              quantity: item.flow_quantity,
            })),
            backgroundColor: 'rgba(255, 152, 0, 0.6)',
            borderColor: '#FF9800',
          },
        ],
      },
      options: {
        responsive: true,
        plugins: {
          tooltip: {
            callbacks: {
              label: (context: TooltipItem<'bubble'>) => {
                const point = context.raw as {
                  x: number
                  y: number
                  r: number
                  label: string
                  quantity: number
                }
                return `${point.label}: ${point.quantity} units`
              },
            },
          },
        },
        scales: {
          x: { display: false },
          y: { display: false },
        },
      },
    })
  } catch (error) {
    console.error('Error creating flow map chart:', error)
  }
}

onMounted(async () => {
  await createBranchVolumeChart()
  await createTopProductsChart()
  await createFlowMapChart()
})
</script>
