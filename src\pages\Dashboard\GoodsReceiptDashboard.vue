<template>
  <q-page class="q-pa-md">
    <div class="text-h4 q-mb-md">📥 Goods Receipt Dashboard</div>

    <div class="row q-gutter-md">
      <!-- Volume by Month Chart -->
      <div class="col-12 col-md-6">
        <q-card class="full-height">
          <q-card-section>
            <div class="text-h6">Monthly Receipt Volume</div>
            <canvas ref="volumeChartRef"></canvas>
          </q-card-section>
        </q-card>
      </div>

      <!-- Top Suppliers Chart -->
      <div class="col-12 col-md-6">
        <q-card class="full-height">
          <q-card-section>
            <div class="text-h6">Top Suppliers</div>
            <canvas ref="suppliersChartRef"></canvas>
          </q-card-section>
        </q-card>
      </div>

      <!-- Value by Month Chart -->
      <div class="col-12">
        <q-card>
          <q-card-section>
            <div class="text-h6">Receipt Value by Month</div>
            <canvas ref="valueChartRef"></canvas>
          </q-card-section>
        </q-card>
      </div>
    </div>
  </q-page>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { Chart, registerables } from 'chart.js'
import { api } from 'src/boot/axios'

Chart.register(...registerables)

// ✅ Interfaces
interface VolumeByMonth {
  month: string
  total_quantity: number
  receipt_count: number
  total_value: number
}

interface TopSupplier {
  supplier_id: number
  supplier_name: string
  receipt_count: number
  total_quantity: number
  total_value: number
}

interface ValueByMonth {
  month: string
  total_value: number
  avg_cost_price: number
  item_count: number
}

// ✅ Refs
const volumeChartRef = ref<HTMLCanvasElement>()
const suppliersChartRef = ref<HTMLCanvasElement>()
const valueChartRef = ref<HTMLCanvasElement>()

// ✅ Charts
const createVolumeChart = async () => {
  try {
    const response = await api.get('/dashboard/goods-receipt/volume-by-month')
    const data: VolumeByMonth[] = response.data

    new Chart(volumeChartRef.value!, {
      type: 'line',
      data: {
        labels: data.map((item) => item.month),
        datasets: [
          {
            label: 'Total Quantity',
            data: data.map((item) => item.total_quantity),
            borderColor: '#26A69A',
            backgroundColor: 'rgba(38, 166, 154, 0.1)',
            tension: 0.4,
          },
          {
            label: 'Receipt Count',
            data: data.map((item) => item.receipt_count),
            borderColor: '#AB47BC',
            backgroundColor: 'rgba(171, 71, 188, 0.1)',
            yAxisID: 'y1',
            tension: 0.4,
          },
        ],
      },
      options: {
        responsive: true,
        scales: {
          y: { beginAtZero: true },
          y1: {
            type: 'linear',
            display: true,
            position: 'right',
            beginAtZero: true,
          },
        },
      },
    })
  } catch (error) {
    console.error('Error creating volume chart:', error)
  }
}

const createSuppliersChart = async () => {
  try {
    const response = await api.get('/dashboard/goods-receipt/top-suppliers')
    const data: TopSupplier[] = response.data.slice(0, 10)

    new Chart(suppliersChartRef.value!, {
      type: 'bar',
      data: {
        labels: data.map((item) => item.supplier_name),
        datasets: [
          {
            label: 'Receipt Count',
            data: data.map((item) => item.receipt_count),
            backgroundColor: '#26A69A',
          },
        ],
      },
      options: {
        responsive: true,
        indexAxis: 'y',
        scales: {
          x: { beginAtZero: true },
        },
      },
    })
  } catch (error) {
    console.error('Error creating suppliers chart:', error)
  }
}

const createValueChart = async () => {
  try {
    const response = await api.get('/dashboard/goods-receipt/value-by-month')
    const data: ValueByMonth[] = response.data

    new Chart(valueChartRef.value!, {
      type: 'bar',
      data: {
        labels: data.map((item) => item.month),
        datasets: [
          {
            label: 'Total Value',
            data: data.map((item) => item.total_value),
            backgroundColor: '#4CAF50',
          },
        ],
      },
      options: {
        responsive: true,
        scales: {
          y: { beginAtZero: true },
        },
      },
    })
  } catch (error) {
    console.error('Error creating value chart:', error)
  }
}

// ✅ On mounted
onMounted(async () => {
  await createVolumeChart()
  await createSuppliersChart()
  await createValueChart()
})
</script>
