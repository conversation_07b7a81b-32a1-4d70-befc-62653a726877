<template>
  <q-dialog v-model="isOpen">
    <q-card style="max-width: 1000px; width: 750px">
      <q-card-section>
        <div class="row items-center justify-between">
          <div class="text-h6 text-weight-bold">บริษัทจำหน่ายสินค้า</div>
          <q-btn icon="close" @click="closeDialog" flat rounded />
        </div>
      </q-card-section>
      <q-separator />
      <q-card-section style="overflow: auto; max-height: 80vh">
        <div class="gap-container">
          <div class="row q-gutter-sm items-center">
            <div class="col-8">
              <SearchGRDialogComponent v-model="searchQuery" placeholder="ค้นหา">
              </SearchGRDialogComponent>
            </div>

            <div>
              <FilterGRDialogComponent v-model="selectedFilter" :filterOptions="filterOptions" />
            </div>
          </div>
        </div>

        <!--รายการสินค้า-->
        <div class="gap-container">
          <div class="shadow-2">
            <q-table
              flat
              class="body-table"
              :rows="supplierStore.suppliers"
              :columns="columns"
              row-key="id"
              style="height: 300px"
              @row-click="open3"
              :table-row-class-name="getRowClass"
              :pagination="pagination"
              :rows-per-page-options="[]"
            >
            </q-table>
          </div>
        </div>
      </q-card-section>
      <q-card-actions align="center">
        <q-btn class="btn-accept" dense flat label="บันทึก" color="white" @click="addSelectedPO" />

        <q-btn class="btn-cancel" dense flat label="ยกเลิก" color="white" @click="closeDialog" />
      </q-card-actions>
    </q-card>
  </q-dialog>
</template>
<script setup lang="ts">
import { computed, ref, watch } from 'vue'

import { Notify, type QTableColumn } from 'quasar'
import FilterGRDialogComponent from './FilterGRDialogComponent.vue'
import SearchGRDialogComponent from './SearchGRDialogComponent.vue'
import { useGoodsReceiptStore } from 'src/stores/orders/goodsreceipt'
import { useSupplierStore } from 'src/stores/inventory/supplier'
import type { Supplier } from 'src/types/supplier'
import { useAuthStore } from 'src/stores/auth/authStore'
import { useUserStore } from 'src/stores/users/userStore'
import type { GoodsReceiptDetail } from 'src/types/goodsReceiptDatail'

// Props and emits for v-model support
interface Props {
  modelValue: boolean
  mode?: string
}

const props = defineProps<Props>()
const emit = defineEmits<{
  'update:modelValue': [value: boolean]
  'distributor-selected': [distributor: Supplier]
}>()

// Computed property for v-model
const isOpen = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value),
})

const supplierStore = useSupplierStore()
const grStore = useGoodsReceiptStore()
const authStore = useAuthStore()
const userStore = useUserStore()
const selectedFilter = ref<string>('')
const searchQuery = ref('')
const type = ref('')

const selectedRowId = ref<number | null>(null)
const pagination = ref({
  rowsPerPage: 12,
})
watch(
  () => props.modelValue,
  async (newValue) => {
    if (newValue) {
      // Reset selection when dialog opens
      selectedRowId.value = null
      grStore.distributor = {
        id: 0,
        supplier_number: '',
        name: '',
        address: '',
        tel: '',
        tax_number: '',
        contact_name: '',
        fax: '',
        email: '',
        type: {
          id: 0,
          name: '',
        },
        isactive: false,
        credit_days: 0,
      }
      await userStore.fetchUsers()
      type.value = '2'
      await supplierStore.filterSuppliers(searchQuery.value, selectedFilter.value, type.value)
    }
  },
)

watch([searchQuery, selectedFilter, type], async ([search, filter, type]) => {
  if (props.modelValue) {
    search.trim()
    await supplierStore.filterSuppliers(search, filter, type)
  } else {
    await supplierStore.loadSuppliers()
  }
})

const addSelectedPO = () => {
  if (grStore.distributor && grStore.distributor.id > 0) {
    // Emit the selected distributor
    emit('distributor-selected', grStore.distributor)
    //check chage distributor
    if (props.mode === 'change') {
      if (grStore.form.gr_details.length == 0) {
        grStore.form.distributor = grStore.distributor
        closeDialog()
        return
      } else {
        grStore.form.gr_details.forEach((item: GoodsReceiptDetail) => {
          if (item.product.distributor !== grStore.distributor) {
            Notify.create({
              color: 'negative',
              message:
                'ไม่สามารถเปลี่ยนผู้จำหน่ายได้ เนื่องจากมีรายการสินค้าที่ไม่ใช่ของผู้จำหน่ายที่เลือก ต้องลบรายการสินค้าก่อน',
              icon: 'warning',
            })
          }
        })
      }
      closeDialog()
      return
    }
    // Set form data
    grStore.resetForm()
    grStore.form.distributor = grStore.distributor
    grStore.form.date_document = new Date()
    grStore.form.credit_date = new Date()
    grStore.form.tax_invoice_date = new Date()
    grStore.form.gr_total = 0.0
    grStore.form.credit_days = grStore.distributor.credit_days

    const currentUser = computed(() => {
      return authStore.currentUser || userStore.currentUser
    })

    grStore.form.user = userStore.users.find((user) => user.id === currentUser.value?.id) ?? {
      id: 0,
      name: '',
      password: '',
      tel: '',
      role: '',
      hour_work: 0,
      sick_level: 0,
      personal_leave: 0,
      sick_leave_remaining: 0,
      personal_leave_remaining: 0,
      image: '',
      day_off: '',
      branch: {
        id: 0,
        name: '',
        address: '',
        contact_name: '',
        contact_phone: '',
      },
    }

    grStore.form.branch = currentUser.value?.branch ?? {
      id: 0,
      name: '',
      address: '',
      contact_name: '',
      contact_phone: '',
    }
  }
  closeDialog()
}

const filterOptions = ref([
  { label: 'รหัส', value: 'supplier_number' },
  { label: 'ชื่อบริษัทจำหน่าย', value: 'name' },
  { label: 'ที่อยู่', value: 'address' },
])

const columns = <QTableColumn[]>[
  {
    name: 'supplier_number',
    label: 'รหัส',
    field: 'supplier_number',
    align: 'left' as const,
    sortable: true,
  },
  {
    name: 'name',
    label: 'ชื่อ',
    field: 'name',
    align: 'left' as const,
    sortable: true,
  },
  {
    name: 'address',
    label: 'ที่อยู่',
    field: 'address',
    align: 'left' as const,
  },
]
function getRowClass(row: Supplier) {
  console.log(row)
  return row.id === selectedRowId.value ? 'selected-row' : ''
}
function open3(_evt: Event, row: Supplier) {
  console.log('🚀 Clicked Row Object:', row)

  // Toggle selection: if clicking the same row, deselect it
  if (selectedRowId.value === row.id) {
    selectedRowId.value = null
    // Reset to empty distributor object instead of null
    grStore.distributor = {
      id: 0,
      supplier_number: '',
      name: '',
      address: '',
      tel: '',
      tax_number: '',
      contact_name: '',
      fax: '',
      email: '',
      type: {
        id: 0,
        name: '',
      },
      isactive: false,
      credit_days: 0,
    }
  } else {
    // Select the new row
    selectedRowId.value = row.id
    grStore.distributor = row
  }

  // orderStore.formOrderItems.product = row.product
  // stockStore.formOrderItems = row
  // console.log(orderStore.formOrderItems)
  // dialogBtn.openPO('add')
  // console.log('Open Add ')
}

const closeDialog = () => {
  isOpen.value = false
}
// const cancelDialog = () => {
//   orderStore.editOrderItems = JSON.parse(JSON.stringify(orderStore.orderItems));
//   orderStore.editOrderItems2 = []
//   dialogBtnProduct.closeProduct()
// }
</script>
<style scoped>
.selected-row {
  background-color: #36b54d !important;
  color: white !important;
  /* สีเขียว */
}

.selected-row td {
  background-color: #36b54d !important;
  color: white !important;
}

.selected-row:hover {
  background-color: #2d9a42 !important;
  color: white !important;
}

.selected-row:hover td {
  background-color: #2d9a42 !important;
  color: white !important;
}

.container {
  background-color: #deecff;
  padding: 16px;
  border-bottom-left-radius: 10px;
  border-bottom-right-radius: 10px;
  border-top-left-radius: 0px;
  border-top-right-radius: 0px;
}

.container-header {
  background-color: #294888;
  padding-left: 16px;
  border-top-left-radius: 10px;
  border-top-right-radius: 10px;
  height: 55px;
}

.mini-container {
  padding-left: 10px;
  padding-right: 10px;
  background-color: white;
  border-radius: 5px;
}

.input-container {
  padding-left: 10px;
  padding-right: 10px;
  background-color: white;
  margin-top: 10px;
  margin-bottom: 10px;
  border-radius: 5px;
}

.btn-accept {
  background-color: #36b54d;
  margin-right: 10px;
}

.btn-cancel {
  background-color: #9c2c2c;
  margin-right: 10px;
}

.gap-container {
  margin-bottom: 20px;
}

.dialog-container {
  border-radius: 10px;
}

.container-search {
  display: flex;
  align-items: center;
  padding-left: 10px;
  padding-right: 10px;
  margin-top: 10px;
  margin-bottom: 5px;
  border-radius: 5px;
  background-color: #294888;
  width: 400px;
  height: 50px;
}

.input-container-search {
  padding-left: 10px;
  padding-right: 10px;
  background-color: white;
  margin-top: 5px;
  margin-bottom: 5px;
  border-radius: 5px;
  height: 35px;
  width: 250px;
}

.column-table {
  height: 40px;
  background-color: #294888;
  color: white;
}

.radio-container {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  margin-left: 20px;
}

.q-radio {
  margin-right: 15px;
}

.custom-table {
  border-radius: 10px;
  overflow: hidden;
}

:deep(.q-table thead tr) {
  background-color: #294888;
  color: #deecff;
}

.body-table {
  background-color: #deecff;
}

/* Ensure row selection works properly */
:deep(.q-table tbody tr.selected-row) {
  background-color: #36b54d !important;
  color: white !important;
}

:deep(.q-table tbody tr.selected-row td) {
  background-color: #36b54d !important;
  color: white !important;
}

:deep(.q-table tbody tr.selected-row:hover) {
  background-color: #2d9a42 !important;
  color: white !important;
}

:deep(.q-table tbody tr.selected-row:hover td) {
  background-color: #2d9a42 !important;
  color: white !important;
}

/* Add hover effect for non-selected rows */
:deep(.q-table tbody tr:not(.selected-row):hover) {
  background-color: #c8e6ff !important;
  cursor: pointer;
}

:deep(.q-table tbody tr:not(.selected-row):hover td) {
  background-color: #c8e6ff !important;
}
</style>
