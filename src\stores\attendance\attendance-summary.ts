import { defineStore } from 'pinia'
import { AttendanceService } from 'src/services/attendanceService'
import type { AttendanceSummary } from 'src/types/attendancesummary'

export const useAttendanceSummaryStore = defineStore('attendanceSummary', {
  state: () => ({
    summaryData: [] as AttendanceSummary[],
    loading: false,
    error: null as string | null,
    dateRange: {
      from: '',
      to: ''
    }
  }),

  getters: {
    getSummaryByUserId: (state) => (userId: number) => {
      return state.summaryData.find(summary => summary.userId === userId)
    },
    
    getDateRange: (state) => state.dateRange
  },

  actions: {
    async fetchAttendanceSummary(startDate: string, endDate: string) {
      this.loading = true
      this.error = null
      this.dateRange = {
        from: startDate,
        to: endDate
      }
      
      try {
        const data = await AttendanceService.getAttendanceByDateRange(startDate, endDate)
        this.summaryData = data
        return data
      } catch (error) {
        this.error = 'Failed to fetch attendance summary'
        console.error('Fetch attendance summary error:', error)
        return []
      } finally {
        this.loading = false
      }
    },
    
    resetState() {
      this.summaryData = []
      this.loading = false
      this.error = null
      this.dateRange = {
        from: '',
        to: ''
      }
    }
  }
})