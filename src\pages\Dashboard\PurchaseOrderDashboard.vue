<template>
  <q-page class="q-pa-md">
    <div class="text-h4 q-mb-md">🛒 Purchase Orders Dashboard</div>

    <div class="row q-gutter-md">
      <!-- Summary by Month Chart -->
      <div class="col-12 col-md-6">
        <q-card class="full-height">
          <q-card-section>
            <div class="text-h6">Monthly PO Summary</div>
            <canvas ref="summaryChartRef"></canvas>
          </q-card-section>
        </q-card>
      </div>

      <!-- Top Products Chart -->
      <div class="col-12 col-md-6">
        <q-card class="full-height">
          <q-card-section>
            <div class="text-h6">Top Ordered Products</div>
            <canvas ref="topProductsChartRef"></canvas>
          </q-card-section>
        </q-card>
      </div>

      <!-- Supplier Ratio Chart -->
      <div class="col-12">
        <q-card>
          <q-card-section>
            <div class="text-h6">Supplier Order Ratio</div>
            <canvas ref="supplierRatioChartRef"></canvas>
          </q-card-section>
        </q-card>
      </div>
    </div>
  </q-page>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { Chart, registerables } from 'chart.js'
import { api } from 'src/boot/axios'
import type { TooltipItem } from 'chart.js'

Chart.register(...registerables)

const summaryChartRef = ref<HTMLCanvasElement | null>(null)
const topProductsChartRef = ref<HTMLCanvasElement | null>(null)
const supplierRatioChartRef = ref<HTMLCanvasElement | null>(null)

// ---------- Interfaces ----------
interface SummaryByMonth {
  month: string
  order_count: number
  total_value: number
}

interface TopProduct {
  product_name: string
  total_ordered: number
  total_value: number
}

interface SupplierRatio {
  supplier_name: string
  percentage: number
}

// ---------- Summary Chart ----------
const createSummaryChart = async () => {
  try {
    const response = await api.get<SummaryByMonth[]>('/dashboard/purchase-orders/summary-by-month')
    const data = response.data

    if (!summaryChartRef.value || !Array.isArray(data)) return

    new Chart(summaryChartRef.value, {
      type: 'line',
      data: {
        labels: data.map((item) => item.month),
        datasets: [
          {
            label: 'Order Count',
            data: data.map((item) => item.order_count),
            borderColor: '#2196F3',
            backgroundColor: 'rgba(33, 150, 243, 0.1)',
            tension: 0.4,
          },
          {
            label: 'Total Value',
            data: data.map((item) => item.total_value),
            borderColor: '#4CAF50',
            backgroundColor: 'rgba(76, 175, 80, 0.1)',
            yAxisID: 'y1',
            tension: 0.4,
          },
        ],
      },
      options: {
        responsive: true,
        scales: {
          y: { beginAtZero: true },
          y1: { type: 'linear', display: true, position: 'right', beginAtZero: true },
        },
      },
    })
  } catch (error) {
    console.error('Error creating summary chart:', error)
  }
}

// ---------- Top Products Chart ----------
const createTopProductsChart = async () => {
  try {
    const response = await api.get<TopProduct[]>('/dashboard/purchase-orders/top-products?limit=5')
    const data = response.data

    if (!topProductsChartRef.value || !Array.isArray(data)) return

    new Chart(topProductsChartRef.value, {
      type: 'bar',
      data: {
        labels: data.map((item) => item.product_name),
        datasets: [
          {
            label: 'Total Ordered',
            data: data.map((item) => item.total_ordered),
            backgroundColor: '#9C27B0',
          },
          {
            label: 'Total Value',
            data: data.map((item) => item.total_value),
            backgroundColor: '#E91E63',
            yAxisID: 'y1',
          },
        ],
      },
      options: {
        responsive: true,
        scales: {
          y: { beginAtZero: true },
          y1: { type: 'linear', display: true, position: 'right', beginAtZero: true },
        },
      },
    })
  } catch (error) {
    console.error('Error creating top products chart:', error)
  }
}

// ---------- Supplier Ratio Chart ----------
const createSupplierRatioChart = async () => {
  try {
    const response = await api.get<SupplierRatio[]>('/dashboard/purchase-orders/supplier-ratio')
    const data = response.data

    if (!supplierRatioChartRef.value || !Array.isArray(data)) return

    new Chart(supplierRatioChartRef.value, {
      type: 'pie',
      data: {
        labels: data.map((item) => item.supplier_name),
        datasets: [
          {
            data: data.map((item) => item.percentage),
            backgroundColor: [
              '#26A69A',
              '#AB47BC',
              '#4CAF50',
              '#FF9800',
              '#F44336',
              '#2196F3',
              '#9C27B0',
              '#607D8B',
              '#795548',
              '#E91E63',
            ],
          },
        ],
      },
      options: {
        responsive: true,
        plugins: {
          legend: { position: 'bottom' },
          tooltip: {
            callbacks: {
              label: (context: TooltipItem<'pie'>) => {
                return `${context.label}: ${context.parsed}%`
              },
            },
          },
        },
      },
    })
  } catch (error) {
    console.error('Error creating supplier ratio chart:', error)
  }
}

onMounted(async () => {
  await createSummaryChart()
  await createTopProductsChart()
  await createSupplierRatioChart()
})
</script>
