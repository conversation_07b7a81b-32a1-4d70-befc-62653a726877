import type { GoodsReceipt } from "./goodsReceipt";
import type { Product } from "./product";
import type { PurchaseOrderItem } from "./purchaseOrderitem";

export interface GoodsReceiptDetail {
  id: number;
  po_details: PurchaseOrderItem;
  gr: GoodsReceipt;
  product: Product;
  lot_number_before: string;
  receive_quantity: number;
  receive_unit: string;
  has_discount: boolean;
  receive_price_before_tax: number;
  receive_price_after_discount: number;
  has_free: boolean;
  free_quantity: number;
  free_unit: string;
  production_number: string;
  mfg_date: Date;
  exp_date: Date;
  total_receive_quantity: number;
  cost_unit: number;
  total_price_product: number;
  gr_total: number;
}
