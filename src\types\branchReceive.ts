import type { Branch } from "./branch";
import type { BranchReceiveDetails } from "./branchReceiveDetails";
import type { StockTransferSlip } from "./stockTransferSlip";
import type { user } from "./user";

export interface BranchReceive {
  id: number;
  sts: StockTransferSlip;
  code: string;
  status: string;
  source_branch: Branch;
  destination_branch: Branch;
  sts_code: string;
  receive_date: string;
  complete_date: string;
  user: user;
  note: string;
  br_details: BranchReceiveDetails[];
}

