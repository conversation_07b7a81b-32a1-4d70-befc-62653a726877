import { defineStore } from 'pinia'

export const useDialogPO = defineStore('dialog', {
  state: () => ({
    isOpen: false,
    isOpenDetail: false,
    mode: 'add',
  }),
  actions: {
    open(mode = 'add') {
      this.isOpen = true
      this.mode = mode
    },
    openDetail(mode = 'add') {
      this.isOpenDetail = true
      this.mode = mode
    },
    close() {
      this.isOpen = false
    },
    closeDetail() {
      this.isOpenDetail = false
    },
  },
})
