<template>
  <q-dialog v-model="isOpen" maximized>
    <q-card style="max-width: 1100px">
      <q-card-section>
        <div class="row items-center justify-between">
          <div class="text-h6 text-weight-bold">ร้องขอสินค้า</div>
          <q-btn icon="close" @click="closeDialog" flat rounded />
        </div>
      </q-card-section>
      <q-separator />
      <template v-if="store.form.user">
        <q-card-section :style="{ overflow: 'auto', maxHeight: '80vh' }">
          <!-- รายละเอียดบริษัท -->
          <div class="gap-container">
            <div class="text-white shadow-2 container-header row items-center">
              รายละเอียดการสั่งโอน
            </div>
            <div class="shadow-2 container">
              <div class="row q-col-gutter-md">
                <div class="col-12 col-md-1 q-mt-md" style="margin-left: 20px">เลขที่</div>
                <div class="col-12 col-md-4">
                  <q-input
                    class="input-container"
                    v-model="store.form.id"
                    dense
                    borderless
                    style="width: 310px"
                    readonly
                  />
                </div>
                <!--สถานะ-->
                <div class="col-1 q-pr-md flex flex-center" style="margin-left: 10px">สถานะ</div>
                <div class="row q-col-gutter-sm">
                  <q-radio
                    keep-color
                    v-model="store.form.status"
                    val="เตรียมรายการ"
                    label="เตรียมรายการ"
                    color="orange"
                    style="color: orange"
                    size="sm"
                    disable
                  />
                  <q-radio
                    keep-color
                    v-model="store.form.status"
                    val="ดำเนินการ"
                    label="ดำเนินการ"
                    color="blue"
                    style="color: royalblue"
                    size="sm"
                    disable
                  />
                  <q-radio
                    keep-color
                    v-model="store.form.status"
                    val="เสร็จสมบูรณ์"
                    label="เสร็จสมบูรณ์"
                    color="green"
                    style="color: green"
                    size="sm"
                    disable
                  />
                  <q-radio
                    keep-color
                    v-model="store.form.status"
                    val="ยกเลิก"
                    label="ยกเลิก"
                    color="red"
                    style="color: red"
                    size="sm"
                    disable
                  />
                </div>
              </div>

              <!-- แถว: วันที่ + พนักงาน (ซ้าย) | หมายเหตุ (ขวา) -->
              <div class="row q-col-gutter-md">
                <!-- คอลัมน์ซ้าย: วันที่ + พนักงาน -->
                <div class="col-12 col-md-5" style="margin-top: 10px; margin-left: 10px">
                  <!-- วันที่ -->
                  <div class="row items-center q-gutter-sm q-mb-sm">
                    <div class="col-12 col-md-2 q-mt-md" style="margin-left: 18px">วันที่</div>
                    <div class="col-12 col-md-9">
                      <q-input
                        dense
                        borderless
                        class="input-container"
                        v-model="formattedOrderDate"
                        style="margin-left: 5px"
                        readonly
                      >
                        <q-icon
                          name="event"
                          size="md"
                          color="black"
                          style="cursor: pointer; margin-top: 5px"
                        >
                          <q-popup-proxy transition-show="scale" transition-hide="scale">
                            <q-date v-model="formattedOrderDate" mask="DD/MM/YYYY" color="teal" />
                          </q-popup-proxy>
                        </q-icon>
                      </q-input>
                    </div>
                  </div>

                  <!-- พนักงาน -->
                  <div class="row items-center q-gutter-sm q-mb-sm">
                    <div class="col-12 col-md-2 q-mt-md" style="margin-left: 18px">พนักงาน</div>
                    <div class="col-12 col-md-9">
                      <q-input
                        dense
                        borderless
                        class="input-container"
                        v-model="store.form.user.name"
                        style="margin-left: 5px"
                        readonly
                      />
                    </div>
                  </div>
                </div>

                <!-- คอลัมน์ขวา: หมายเหตุ -->
                <div class="col-12 col-md-6">
                  <div class="row items-start q-gutter-sm" style="margin-top: 5px">
                    <div class="col-12 col-md-2 q-mt-md" style="margin-left: 20px">หมายเหตุ</div>
                    <div class="col-12 col-md-9">
                      <q-input
                        class="input-container"
                        v-model="store.form.note"
                        dense
                        borderless
                        type="textarea"
                        style="width: 100%; margin-left: 20px"
                      />
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <!-- การสั่งซื้อ -->

          <div class="row">
            <div class="gap-container-half">
              <div class="text-white shadow-2 container-header row items-center">สาขาต้นทาง</div>
              <div class="shadow-2 container">
                <div class="row q-col-gutter-md">
                  <div class="col-12 col-md-2 q-mt-md" style="margin-left: 20px">สาขา</div>
                  <div class="col-12 col-md-9">
                    <q-input
                      class="input-container col-9"
                      v-model="store.form.source_branch.name"
                      dense
                      borderless
                      map-options
                      readonly
                    />
                  </div>
                </div>

                <div class="row q-col-gutter-md" style="margin-top: 10px">
                  <div class="col-12 col-md-2 q-mt-md" style="margin-left: 20px">ชื่อผู้ติดต่อ</div>
                  <div class="col-12 col-md-9">
                    <q-input
                      class="input-container col-9"
                      v-model="store.form.source_branch.contact_name"
                      dense
                      borderless
                      type="text"
                      style="width: 100%"
                      readonly
                    />
                  </div>
                </div>

                <div class="row q-col-gutter-md" style="margin-top: 10px">
                  <div class="col-12 col-md-2 q-mt-md" style="margin-left: 20px">ที่อยู่</div>
                  <div class="col-12 col-md-9">
                    <q-input
                      class="input-container"
                      v-model="store.form.source_branch.address"
                      dense
                      borderless
                      type="textarea"
                      style="width: 100%"
                      readonly
                    />
                  </div>
                </div>
              </div>
            </div>
            <div class="gap-container-half" style="margin-left: 20px">
              <div class="text-white shadow-2 container-header row items-center">สาขาปลายทาง</div>
              <div class="shadow-2 container">
                <div class="row q-col-gutter-md">
                  <div class="col-12 col-md-2 q-mt-md" style="margin-left: 20px">สาขา</div>
                  <div class="col-12 col-md-9">
                    <q-select
                      class="input-container col-9"
                      v-model="store.form.destination_branch"
                      :options="branchStore.branchs"
                      option-label="name"
                      option-value="id"
                      dense
                      borderless
                      map-options
                    />
                  </div>
                </div>

                <div class="row q-col-gutter-md" style="margin-top: 10px">
                  <div class="col-12 col-md-2 q-mt-md" style="margin-left: 20px">ชื่อผู้ติดต่อ</div>
                  <div class="col-12 col-md-9">
                    <q-input
                      class="input-container col-9"
                      v-model="destinationBranchContact"
                      dense
                      borderless
                      type="text"
                      style="width: 100%"
                      readonly
                    />
                  </div>
                </div>

                <div class="row q-col-gutter-md" style="margin-top: 10px">
                  <div class="col-12 col-md-2 q-mt-md" style="margin-left: 20px">ที่อยู่</div>
                  <div class="col-12 col-md-9">
                    <q-input
                      class="input-container"
                      v-model="destinationBranchAddress"
                      dense
                      borderless
                      type="textarea"
                      style="width: 100%"
                      readonly
                    />
                  </div>
                </div>
              </div>
            </div>
          </div>
        </q-card-section>
        <q-card-actions align="center">
          <q-btn class="btn-accept" dense flat label="บันทึก" @click="saveDialog" />
          <q-btn class="btn-cancel" dense flat label="ยกเลิก" @click="closeDialog" />
        </q-card-actions>
      </template>
      <template v-else>
        <div class="col-lg-12 row justify-center items-center" style="min-height: 200px">
          <div class="text-h6 text-grey-6">กำลังโหลดข้อมูล...</div>
        </div>
      </template>
    </q-card>
  </q-dialog>
  <STODetailDialog v-model="STODetailDialogOpen" :mode="modeSTODetail"></STODetailDialog>
</template>

<script setup lang="ts">
import { computed, ref, watch } from 'vue'
import { date } from 'quasar' // ใช้ quasar date utility หากต้องการจัดการวันที่
import { useStockTransferOrderStore } from 'src/stores/orders/stocktransferorder'
import { useUserStore } from 'src/stores/users/userStore'
import { useAuthStore } from 'src/stores/auth/authStore'
import { useBranchStore } from 'src/stores/inventory/branch'
import STODetailDialog from 'src/components/dialog/stocktransferorder/STODetailDialog.vue'

const store = useStockTransferOrderStore()
const userStore = useUserStore()
const authStore = useAuthStore()
const branchStore = useBranchStore()

const STODetailDialogOpen = ref(false)
const modeSTODetail = ref('add')

interface Props {
  modelValue: boolean
  mode?: string
}

const props = withDefaults(defineProps<Props>(), {
  grDetails: null,
  mode: 'edit',
})

const emit = defineEmits<{
  'update:modelValue': [value: boolean]
  'item-updated': []
}>()

const isOpen = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value),
})
const closeDialog = () => {
  try {
    // Preserve essential data before closing
    const currentUser = authStore.currentUser || userStore.currentUser
    const currentBranch = store.form.source_branch || currentUser?.branch
    const currentUserData = store.form.user || currentUser

    isOpen.value = false

    // Reset form but preserve user and branch data to prevent undefined errors
    store.resetForm()

    // Restore essential data after reset
    if (currentUserData) {
      store.form.user = currentUserData
    }
    if (currentBranch) {
      store.form.source_branch = currentBranch
    }

    // Initialize destination_branch to prevent undefined errors
    store.form.destination_branch = {
      id: 0,
      name: '',
      address: '',
      contact_name: '',
      contact_phone: '',
    }

    console.log('STO Dialog closed safely with preserved user/branch data')
  } catch (error) {
    console.error('Error in closeDialog:', error)
    // Fallback: just close the dialog without reset if there's an error
    isOpen.value = false
  }
}
const saveDialog = async () => {
  isOpen.value = false
  await store.create()
  await store.fetchSTOByStatus()
  STODetailDialogOpen.value = true
  modeSTODetail.value = 'add'
}

watch(
  () => props.modelValue,
  async (newValue) => {
    if (newValue) {
      try {
        // Ensure user data is loaded
        await userStore.fetchUsers()
        await branchStore.fetchAllBranch()

        // Initialize destination_branch to empty object so user can select from dropdown
        // This prevents undefined errors while allowing proper selection
        store.form.status = 'เตรียมรายการ'
        if (!store.form.destination_branch) {
          store.form.destination_branch = {
            id: 0,
            name: '',
            address: '',
            contact_name: '',
            contact_phone: '',
          }
        }

        // Get current user safely
        const currentUser = authStore.currentUser || userStore.currentUser

        if (currentUser) {
          if (!store.form.user || !store.form.user.id) {
            const foundUser = userStore.users.find((user) => user.id === currentUser.id)
            store.form.user = foundUser || currentUser
          }
          if (!store.form.source_branch || !store.form.source_branch.id) {
            store.form.source_branch = store.form.user.branch ||
              currentUser.branch || {
                id: 0,
                name: '',
                address: '',
              }
          }
        }
        console.log(store.form)
      } catch (error) {
        console.error('Error initializing STO dialog:', error)
      }
    }
  },
)

const formattedOrderDate = computed({
  get() {
    // ตรวจสอบว่า store.form.order_date มีค่าไหม
    const dateValue = store.form.request_date ? store.form.request_date : new Date()
    return date.formatDate(dateValue, 'DD/MM/YYYY') // แปลงจาก Date เป็น string ในรูปแบบ dd/mm/yyyy
  },
  set(value: string) {
    const parts = value.split('/') // แยกค่าโดยใช้ '/'

    if (parts.length === 3) {
      const [day, month, year] = parts
      const newDate = new Date(`${year}-${month}-${day}`) // แปลงกลับเป็น Date
      store.form.request_date = newDate // อัพเดทค่าใน store
    } else {
      store.form.request_date = new Date() // หากรูปแบบไม่ถูกต้อง, ใช้วันที่ปัจจุบัน
    }
  },
})

// Computed property to safely access destination branch address
const destinationBranchAddress = computed(() => {
  return store.form.destination_branch?.address || ''
})

// Computed property to safely access destination branch contact
// Note: Branch type doesn't have contact field, so this will be empty for now
const destinationBranchContact = computed(() => {
  return store.form.destination_branch?.contact_name || '' // Branch type doesn't have contact field
})
</script>

<style scoped>
.container {
  background-color: #deecff;
  padding: 16px;
  border-bottom-left-radius: 10px;
  border-bottom-right-radius: 10px;
  border-top-left-radius: 0px;
  border-top-right-radius: 0px;
}

.container-header {
  background-color: #294888;
  padding-left: 16px;
  border-top-left-radius: 10px;
  border-top-right-radius: 10px;
  height: 55px;
}

.mini-container {
  padding-left: 10px;
  padding-right: 10px;
  background-color: white;
  border-radius: 5px;
}

.input-container {
  padding-left: 10px;
  padding-right: 10px;
  background-color: white;
  border-radius: 5px;
}

.input-container-2 {
  padding-left: 5px;
  padding-right: 5px;
  background-color: white;
  border-radius: 5px;
}

.btn-accept {
  background-color: #36b54d;
  margin-right: 10px;
}

.btn-cancel {
  background-color: #9c2c2c;
  margin-right: 10px;
}

.gap-container {
  margin-bottom: 20px;
}

.gap-container-half {
  margin-bottom: 20px;
  width: 515px;
}

.dialog-container {
  border-radius: 10px;
}

.input-container-v2 {
  padding-left: 10px;
  padding-right: 10px;
  border-radius: 5px;
  background-color: #83a7d8;
}

.input-container-v3 {
  padding-left: 10px;
  padding-right: 10px;
  margin-top: 5px;
  margin-bottom: 5px;
  border-radius: 5px;
  background-color: #d6e4f6;
  width: 150px;
}

.width-column {
  width: 300px;
}

.width-column-2 {
  width: 390px;
}

.width-column-3 {
  width: 350px;
}

.column-table {
  height: 40px;
  background-color: #294888;
  color: white;
}

.column-table-2 {
  height: 40px;
  padding-left: 10px;
  padding-right: 10px;
  background-color: white;
  border: 2px solid #294888;
}
</style>
