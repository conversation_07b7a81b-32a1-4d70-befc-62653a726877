import type { Branch } from "./branch";
import type { GoodsReceiptDetail } from "./goodsReceiptDatail";
import type { PaymentGoodsReceipt } from "./paymentGoodsReceipt";
import type { PurchaseOrder } from "./purchaseOrder"
import type { Supplier } from "./supplier"
import type { user } from "./user";

export interface GoodsReceipt {
  id: number;
  code: string;
  po: PurchaseOrder;
  distributor: Supplier;
  branch: Branch;
  po_date: Date;
  po_code: string;
  date_document: Date;
  receive_date: Date;
  po_total: number;
  tax: number;
  tax_total: number;
  gr_total: number;
  gr_details_total: number;
  tax_invoice_number: string;
  tax_invoice_date: Date;
  credit_date: Date;
  credit_due_date: Date;
  credit_days: number;
  status: string;
  user: user;
  is_tax_invoice: boolean; // ใบรับสินค้ามีภาษีหรือไม่
  order_discount: number; // ส่วนลดท้ายบิล (บาท)
  product_price_tax: string; // 'รวมภาษี' หรือ 'ไม่รวมภาษี' ราคารายการสินค้า
  vat_percent: number; // เปอร์เซ็นต์ภาษี เช่น 7
  is_manual_discount_before_tax: boolean; // ติ๊กส่วนลดท้ายบิลก่อนภาษี (ถ้าสร้างเอง)
  order_discount_tax: string; // 'ก่อนภาษี' หรือ 'หลังภาษี'
  is_before_tax_discount: string; // ติ๊กส่วน % ส่วนลด ก่อนภาษี
  before_tax_discount_percent: number; // กรณีระบุ % ส่วนลด
  before_tax_discount_amount: number; // กรณีระบุส่วนลดจำนวนเงิน
  is_manual_discount_after_tax: boolean; // ติ๊กส่วนลดท้ายบิลหลังภาษี (ถ้าสร้างเอง)
  is_after_tax_discount: string; // ติ๊กส่วนลด หลังภาษี
  after_tax_discount_percent: number; // ส่วนลดท้ายบิลหลังภาษี กรณีระบุ % ส่วนลด
  after_tax_discount_amount: number; // กรณีระบุส่วนลดจำนวนเงิน
  is_discount_applied: boolean; //ใช้ส่วนลดในการคำนวณทุนสินค้า
  is_tax_included: boolean; //เพิ่มภาษีลงในราคาทุนสินค้า
  note: string;
  gr_details: GoodsReceiptDetail[];
  payment: PaymentGoodsReceipt[];
}
