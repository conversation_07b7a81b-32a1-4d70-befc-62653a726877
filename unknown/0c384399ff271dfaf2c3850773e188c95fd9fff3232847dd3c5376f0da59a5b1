import axios from 'axios'
import type { GoodsReceipt } from 'src/types/goodsReceipt'
import type { Stock } from 'src/types/stock'


const API_URL = 'http://localhost:3000/gr'
const API_URL2 = 'http://localhost:3000/gr_detail' // เปลี่ยนให้ตรงกับ backend ของคุณ
// เปลี่ยนให้ตรงกับ backend ของคุณ


export const goodsReceiptService = {
  // ดึงข้อมูลรายการ GoodsReceipt ทั้งหมด
  async getAll(): Promise<GoodsReceipt[]> {
    const response = await axios.get(API_URL)
    return response.data
  },

  // ดึงข้อมูล Purchase Order ตาม ID
  async getById(id: number): Promise<GoodsReceipt> {
    const response = await axios.get(`${API_URL}/${id}`)
    return response.data
  },

  async getByStatus(status: string): Promise<GoodsReceipt[]> {
    const response = await axios.get(`${API_URL}/status/${status}`)
    return response.data
  },

  async getProductByDistrbutor(branchId: number, id: number, search = '', filter = ''): Promise<Stock[]> {
    const response = await axios.post(`${API_URL}/branch/${branchId}/distributor/${id}`, {
      search,
      filter
    })
    return response.data
  },
  async deletePoItems(selectedIds: number[]) {
    console.log(selectedIds)
    await axios.delete(`${API_URL2}/${selectedIds.join(",")}`, { data: { ids: selectedIds } })
  },

  // สร้าง Goods Receipt ใหม่จาก Distributor
  async createFromDistributor(gr: GoodsReceipt): Promise<GoodsReceipt> {
    const payload = {
      ...gr,
      date_document: gr.date_document ? gr.date_document.toISOString() : new Date().toISOString(),
      tax_invoice_date: gr.tax_invoice_date ? gr.tax_invoice_date.toISOString() : null,
      credit_date: gr.credit_date ? gr.credit_date.toISOString() : null,
    }
    const response = await axios.post(API_URL, payload)
    return response.data
  },

  async createFromPO(POId: number, gr: GoodsReceipt): Promise<GoodsReceipt> {
    const payload = {
      ...gr,
      date_document: gr.date_document ? gr.date_document.toISOString() : new Date().toISOString(),
      tax_invoice_date: gr.tax_invoice_date ? gr.tax_invoice_date.toISOString() : null,
      credit_date: gr.credit_date ? gr.credit_date.toISOString() : null,
    }
    const response = await axios.post(`${API_URL}/createByPO/${POId}`, payload)
    return response.data
  },


  // อัปเดต Purchase Order
  async update(id: number, gr: Partial<GoodsReceipt>): Promise<GoodsReceipt> {
    const response = await axios.put(`${API_URL}/${id}`, gr)
    return response.data
  },

  // ลบ Purchase Order
  async delete(id: number): Promise<void> {
    await axios.delete(`${API_URL}/${id}`)
  },

  async filter(search: string, filter: string, startDate: string, endDate: string): Promise<GoodsReceipt[]> {
    try {
      const response = await axios.post(`${API_URL}/filter`, {
        search,
        filter,
        startDate,
        endDate
      })
      return response.data
    } catch (error) {
      console.error("Error filtering purchase orders", error)
      throw error
    }
  },
}
