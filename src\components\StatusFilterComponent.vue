<template>
  <div class="status-filter-container">
    <!-- Checkbox Options -->
    <div class="row justify-left q-mb-sm">
      <q-checkbox v-for="status in statusTypes" :key="status.key" size="xs" v-model="selectedStatuses[status.key]"
        :label="status.label" @update:model-value="updateFilter" class="q-mr-md" />
    </div>

    <!-- Status Tables -->
    <div v-if="showTables">
      <template v-for="status in visibleStatusTypes" :key="status.key">
        <div class="q-mb-lg">
          <span class="text-subtitle1 text-weight-bold q-mb-sm">{{ status.tableTitle }}</span>
          <q-card flat class="custom-table">
            <q-table class="body-table" :rows="status.data" :columns="columns" row-key="id" :pagination="pagination"
              :rows-per-page-options="[]" @row-click="(evt: Event, row: TableData) => $emit('row-click', evt, row)"
              style="height: 100%; max-height: 700px">
            </q-table>
          </q-card>
        </div>
      </template>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted } from 'vue'
import type { QTableColumn } from 'quasar'
import type { PurchaseOrder } from 'src/types/purchaseOrder'
import type { GoodsReceipt } from 'src/types/goodsReceipt'
import type { StockTransferOrder } from 'src/types/stockTransferOrder'
import type { StockTransferSlip } from 'src/types/stockTransferSlip'
import type { BranchReceive } from 'src/types/branchReceive'

// Union type for all possible data types
type TableData = PurchaseOrder | GoodsReceipt | StockTransferOrder | StockTransferSlip | BranchReceive

// Status Type Interface
interface StatusType {
  key: string
  label: string
  tableTitle: string
  data: TableData[]
  defaultSelected?: boolean
}

// Props
interface Props {
  statusTypes: StatusType[]
  columns: QTableColumn[]
  pagination?: {
    rowsPerPage: number
  }
}

const props = withDefaults(defineProps<Props>(), {
  pagination: () => ({ rowsPerPage: 4 })
})

// Emits
const emit = defineEmits<{
  'row-click': [event: Event, row: TableData]
  'status-change': [statuses: Record<string, boolean>]
}>()

// Reactive data
const selectedStatuses = ref<Record<string, boolean>>({})

// Initialize selected statuses based on statusTypes
const initializeStatuses = () => {
  const statuses: Record<string, boolean> = {}
  props.statusTypes.forEach(status => {
    statuses[status.key] = status.defaultSelected ?? false
  })
  selectedStatuses.value = statuses

  // Emit initial status if any are selected by default
  const hasDefaultSelected = Object.values(statuses).some(selected => selected)
  if (hasDefaultSelected) {
    emit('status-change', { ...statuses })
  }
}

// Computed
const showTables = computed(() => {
  return Object.values(selectedStatuses.value).some(selected => selected)
})

const visibleStatusTypes = computed(() => {
  return props.statusTypes.filter(status => selectedStatuses.value[status.key])
})

// Methods
const updateFilter = () => {
  emit('status-change', { ...selectedStatuses.value })
}

// Initialize on mount
onMounted(() => {
  initializeStatuses()
})

// Watch for statusTypes changes
watch(() => props.statusTypes, () => {
  initializeStatuses()
}, { deep: true, immediate: true })
</script>

<style scoped>
.status-filter-container {
  width: 100%;
}

.custom-table {
  overflow: hidden;
}

.body-table {
  background-color: #e1edea;
}

:deep(.q-table thead tr) {
  background-color: #91d2c1;
}
</style>
