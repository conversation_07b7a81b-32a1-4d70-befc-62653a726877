<template>
  <div class="status-filter-container">
    <!-- Checkbox Options -->
    <div class="row justify-left q-mb-sm">
      <q-checkbox v-for="status in statusTypes" :key="status.key" size="xs" v-model="selectedStatuses[status.key]"
        :label="status.label" @update:model-value="updateFilter" class="q-mr-md" />
    </div>

    <!-- Status Tables -->
    <div v-if="showTables">
      <template v-for="status in visibleStatusTypes" :key="status.key">
        <div class="q-mb-lg">
          <span class="text-subtitle1 text-weight-bold q-mb-sm">{{ status.tableTitle }}</span>
          <q-card flat class="custom-table">
            <q-table class="body-table" :rows="status.data" :columns="status.columns || columns" row-key="id"
              :pagination="pagination" :rows-per-page-options="[]"
              @row-click="(evt: Event, row: any) => status.onRowClick ? status.onRowClick(evt, row) : $emit('row-click', evt, row)"
              style="height: 100%; max-height: 700px">

              <!-- Status Badge Template -->
              <template #body-cell-status="props">
                <q-td class="q-gutter-x-sm" style="min-width: 100px; text-align: center">
                  <q-badge :label="props.row.status" :class="{
                    'green-card': props.row.status === 'เสร็จสมบูรณ์',
                    'blue-card': props.row.status === 'ดำเนินการ',
                    'yellow-card': props.row.status === 'เตรียมรายการ',
                    'red-card': props.row.status === 'ยกเลิก',
                  }" class="q-ml-none" style="
                      height: 30px;
                      width: 100px;
                      display: flex;
                      align-items: left;
                      justify-content: flex-start;
                      border-radius: 8px;
                      padding-left: 8px;
                    ">
                  </q-badge>
                </q-td>
              </template>

              <!-- Transfer Status Badge Template -->
              <template #body-cell-transfer_status="props">
                <q-td class="q-gutter-x-sm" style="min-width: 100px; text-align: center">
                  <q-badge :label="props.row.transfer_status" :class="{
                    'purple-card': props.row.transfer_status === 'จัดเตรียมสินค้า',
                    'blue-card': props.row.transfer_status === 'รอส่งรายการสินค้า',
                    'green-transfer-card': props.row.transfer_status === 'สนญ.รับทราบ',
                    'green-transfer-card2': props.row.transfer_status === 'ปลายทางรับทราบ',
                    'red-transfer-card': props.row.transfer_status === 'รายการสินค้ายกเลิก',
                  }" class="q-ml-none" style="
                      height: 30px;
                      width: 100px;
                      display: flex;
                      align-items: left;
                      justify-content: flex-start;
                      border-radius: 8px;
                      padding-left: 8px;
                    ">
                  </q-badge>
                </q-td>
              </template>
            </q-table>
          </q-card>
        </div>
      </template>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted } from 'vue'
import type { QTableColumn } from 'quasar'
import type { PurchaseOrder } from 'src/types/purchaseOrder'
import type { GoodsReceipt } from 'src/types/goodsReceipt'
import type { StockTransferOrder } from 'src/types/stockTransferOrder'
import type { StockTransferSlip } from 'src/types/stockTransferSlip'
import type { BranchReceive } from 'src/types/branchReceive'

// Union type for all possible data types
type TableData = PurchaseOrder | GoodsReceipt | StockTransferOrder | StockTransferSlip | BranchReceive

// Status Type Interface
interface StatusType {
  key: string
  label: string
  tableTitle: string
  data: TableData[]
  defaultSelected?: boolean
  columns?: QTableColumn[] // Optional custom columns for this status type
  onRowClick?: (event: Event, row: any) => void // Optional custom row click handler
}

// Props
interface Props {
  statusTypes: StatusType[]
  columns: QTableColumn[]
  pagination?: {
    rowsPerPage: number
  }
}

const props = withDefaults(defineProps<Props>(), {
  pagination: () => ({ rowsPerPage: 4 })
})

// Emits
const emit = defineEmits<{
  'row-click': [event: Event, row: TableData]
  'status-change': [statuses: Record<string, boolean>]
}>()

// Reactive data
const selectedStatuses = ref<Record<string, boolean>>({})

// Initialize selected statuses based on statusTypes
const initializeStatuses = () => {
  const statuses: Record<string, boolean> = {}
  props.statusTypes.forEach(status => {
    statuses[status.key] = status.defaultSelected ?? false
  })
  selectedStatuses.value = statuses

  // Emit initial status if any are selected by default
  const hasDefaultSelected = Object.values(statuses).some(selected => selected)
  if (hasDefaultSelected) {
    emit('status-change', { ...statuses })
  }
}

// Computed
const showTables = computed(() => {
  return Object.values(selectedStatuses.value).some(selected => selected)
})

const visibleStatusTypes = computed(() => {
  return props.statusTypes.filter(status => selectedStatuses.value[status.key])
})

// Methods
const updateFilter = () => {
  emit('status-change', { ...selectedStatuses.value })
}

// Initialize on mount
onMounted(() => {
  initializeStatuses()
})

// Watch for statusTypes changes
watch(() => props.statusTypes, () => {
  initializeStatuses()
}, { deep: true, immediate: true })
</script>

<style scoped>
.status-filter-container {
  width: 100%;
}

.custom-table {
  overflow: hidden;
}

.body-table {
  background-color: #e1edea;
}

:deep(.q-table thead tr) {
  background-color: #91d2c1;
}

/* Status Badge Styles */
.green-card {
  background-color: #439e62;
  color: white;
}

.yellow-card {
  background-color: #ed9b53;
  color: white;
}

.red-card {
  background-color: #b53638;
  color: white;
}

.blue-card {
  background-color: #83a7d8;
  color: white;
}

.purple-card {
  background-color: #ba8cc9;
  color: #000000;
}

.green-transfer-card {
  background-color: #36b54d;
  color: #000000;
}

.green-transfer-card2 {
  background-color: #42bd86;
  color: #000000;
}

.red-transfer-card {
  background-color: #f55355;
  color: #000000;
}
</style>
