<template>
  <div class="status-filter-container">
    <!-- Checkbox Options -->
    <div class="row justify-left q-mb-sm">
      <q-checkbox size="xs" v-model="selectedStatuses.prepare" label="เตรียมรายการ" @update:model-value="updateFilter"
        class="q-mr-md" />
      <q-checkbox size="xs" v-model="selectedStatuses.process" label="ดำเนินรายการ" @update:model-value="updateFilter"
        class="q-mr-md" />
      <q-checkbox size="xs" v-model="selectedStatuses.all" label="รายการทั้งหมด" @update:model-value="updateFilter" />
    </div>

    <!-- Status Tables -->
    <div v-if="showTables">
      <!-- เตรียมรายการ Table -->
      <div v-if="selectedStatuses.prepare" class="q-mb-lg">
        <span class="text-subtitle1 text-weight-bold q-mb-sm">เตรียมรายการ</span>
        <q-card flat class="custom-table">
          <q-table class="body-table" :rows="prepareData" :columns="columns" row-key="id" :pagination="pagination"
            :rows-per-page-options="[]" @row-click="(evt, row) => $emit('row-click', evt, row)"
            style="height: 100%; max-height: 700px">
          </q-table>
        </q-card>
      </div>

      <!-- ดำเนินรายการ Table -->
      <div v-if="selectedStatuses.process" class="q-mb-lg">
        <span class="text-subtitle1 text-weight-bold q-mb-sm">กำลังดำเนินการ</span>
        <q-card flat class="custom-table">
          <q-table class="body-table" :rows="processData" :columns="columns" row-key="id" :pagination="pagination"
            :rows-per-page-options="[]" @row-click="(evt, row) => $emit('row-click', evt, row)"
            style="height: 100%; max-height: 700px">
          </q-table>
        </q-card>
      </div>

      <!-- รายการทั้งหมด Table -->
      <div v-if="selectedStatuses.all" class="q-mb-lg">
        <span class="text-subtitle1 text-weight-bold q-mb-sm">รายการทั้งหมด</span>
        <q-card flat class="custom-table">
          <q-table class="body-table" :rows="allData" :columns="columns" row-key="id" :pagination="pagination"
            :rows-per-page-options="[]" @row-click="(evt, row) => $emit('row-click', evt, row)"
            style="height: 100%; max-height: 700px">
          </q-table>
        </q-card>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import type { QTableColumn } from 'quasar'
import { GoodsReceipt } from 'src/types/goodsReceipt'
import { StockTransferOrder } from 'src/types/stockTransferOrder'

// Props
interface Props {
  prepareData?: GoodsReceipt[]
  processData?: GoodsReceipt[]
  allData?: GoodsReceipt[]
  columns: QTableColumn[]
  pagination?: {
    rowsPerPage: number
  }
}

const props = withDefaults(defineProps<Props>(), {
  prepareData: () => [],
  processData: () => [],
  allData: () => [],
  pagination: () => ({ rowsPerPage: 4 })
})

// Emits
const emit = defineEmits<{
  'row-click': [event: Event, row: any]
  'status-change': [statuses: { prepare: boolean, process: boolean, all: boolean }]
}>()

// Reactive data
const selectedStatuses = ref({
  prepare: false,
  process: false,
  all: false
})

// Computed
const showTables = computed(() => {
  return selectedStatuses.value.prepare || selectedStatuses.value.process || selectedStatuses.value.all
})

// Methods
const updateFilter = () => {
  emit('status-change', { ...selectedStatuses.value })
}

// Watch for external changes
watch(() => props, () => {
  // Handle any prop changes if needed
}, { deep: true })
</script>

<style scoped>
.status-filter-container {
  width: 100%;
}

.custom-table {
  overflow: hidden;
}

.body-table {
  background-color: #e1edea;
}

:deep(.q-table thead tr) {
  background-color: #91d2c1;
}
</style>
