<template>
  <div class="status-filter-container">
    <!-- Checkbox Options -->
    <div class="row justify-left q-mb-sm">
      <q-checkbox v-for="status in statusTypes" :key="status.key" size="xs" v-model="selectedStatuses[status.key]"
        :label="status.label" @update:model-value="updateFilter" class="q-mr-md" />
    </div>

    <!-- Status Tables -->
    <div v-if="showTables">
      <div v-for="status in statusTypes" :key="status.key" v-if="selectedStatuses[status.key]" class="q-mb-lg">
        <span class="text-subtitle1 text-weight-bold q-mb-sm">{{ status.tableTitle }}</span>
        <q-card flat class="custom-table">
          <q-table class="body-table" :rows="status.data" :columns="columns" row-key="id" :pagination="pagination"
            :rows-per-page-options="[]" @row-click="(evt, row) => $emit('row-click', evt, row)"
            style="height: 100%; max-height: 700px">
          </q-table>
        </q-card>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted } from 'vue'
import type { QTableColumn } from 'quasar'

// Status Type Interface
interface StatusType {
  key: string
  label: string
  tableTitle: string
  data: any[]
  defaultSelected?: boolean
}

// Props
interface Props {
  statusTypes: StatusType[]
  columns: QTableColumn[]
  pagination?: {
    rowsPerPage: number
  }
}

const props = withDefaults(defineProps<Props>(), {
  pagination: () => ({ rowsPerPage: 4 })
})

// Emits
const emit = defineEmits<{
  'row-click': [event: Event, row: any]
  'status-change': [statuses: Record<string, boolean>]
}>()

// Reactive data
const selectedStatuses = ref<Record<string, boolean>>({})

// Initialize selected statuses based on statusTypes
const initializeStatuses = () => {
  const statuses: Record<string, boolean> = {}
  props.statusTypes.forEach(status => {
    statuses[status.key] = status.defaultSelected ?? false
  })
  selectedStatuses.value = statuses

  // Emit initial status if any are selected by default
  const hasDefaultSelected = Object.values(statuses).some(selected => selected)
  if (hasDefaultSelected) {
    emit('status-change', { ...statuses })
  }
}

// Computed
const showTables = computed(() => {
  return Object.values(selectedStatuses.value).some(selected => selected)
})

// Methods
const updateFilter = () => {
  emit('status-change', { ...selectedStatuses.value })
}

// Initialize on mount
onMounted(() => {
  initializeStatuses()
})

// Watch for statusTypes changes
watch(() => props.statusTypes, () => {
  initializeStatuses()
}, { deep: true, immediate: true })
</script>

<style scoped>
.status-filter-container {
  width: 100%;
}

.custom-table {
  overflow: hidden;
}

.body-table {
  background-color: #e1edea;
}

:deep(.q-table thead tr) {
  background-color: #91d2c1;
}
</style>
