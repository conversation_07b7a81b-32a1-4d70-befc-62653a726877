<template>
  <q-dialog v-model="isDialogOpen" persistent>
    <q-card style="max-width: 500px; width: 100%">
      <!-- Header Section -->
      <q-card-section class="row items-center justify-between q-pb-none">
        <div class="text-h6 text-weight-bold">ลงตารางงาน</div>
        <q-btn icon="close" @click="closeDialog" flat rounded />
      </q-card-section>

      <q-separator class="q-mt-md" />

      <!-- Content Section -->
      <q-card-section style="max-height: 70vh" class="scroll q-pa-md">
        <!-- Schedule Header -->
        <div class="gap-container">
          <div class="text-white shadow-2 container-header">ตารางเวลา</div>
          <div class="shadow-2 container">
            <!-- Work Schedule Section -->
            <div class="schedule-section">
              <!-- Check-in Time -->
              <div class="time-section q-mb-lg">
                <div class="text-subtitle2 q-mb-sm text-weight-medium">เวลาเข้างาน</div>
                <div class="time-selector-container">
                  <div class="time-display" @click="openCheckInTimePicker">
                    <div class="time-value">{{ formatTime(scheduleForm.checkInTime) }}</div>
                    <div class="time-period">
                      <div
                        class="period-option"
                        :class="{ active: getTimePeriod(scheduleForm.checkInTime) === 'AM' }"
                      >
                        AM
                      </div>
                      <div
                        class="period-option"
                        :class="{ active: getTimePeriod(scheduleForm.checkInTime) === 'PM' }"
                      >
                        PM
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <!-- Check-out Time -->
              <div class="time-section">
                <div class="text-subtitle2 q-mb-sm text-weight-medium">เวลาออกงาน</div>
                <div class="time-selector-container">
                  <div class="time-display" @click="openCheckOutTimePicker">
                    <div class="time-value">{{ formatTime(scheduleForm.checkOutTime) }}</div>
                    <div class="time-period">
                      <div
                        class="period-option"
                        :class="{ active: getTimePeriod(scheduleForm.checkOutTime) === 'AM' }"
                      >
                        AM
                      </div>
                      <div
                        class="period-option"
                        :class="{ active: getTimePeriod(scheduleForm.checkOutTime) === 'PM' }"
                      >
                        PM
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Loading Indicator -->
        <div v-if="isLoadingSchedule" class="row justify-center q-mt-md">
          <q-spinner-dots size="40px" color="primary" />
          <div class="q-ml-sm text-grey-6">กำลังโหลดข้อมูลตารางงาน...</div>
        </div>

        <!-- Validation Messages -->
        <div v-if="validationMessages.length > 0" class="q-mt-md">
          <q-banner class="bg-negative text-white" rounded>
            <template v-slot:avatar>
              <q-icon name="warning" />
            </template>
            <ul class="q-ma-none q-pl-md">
              <li v-for="message in validationMessages" :key="message">{{ message }}</li>
            </ul>
          </q-banner>
        </div>
      </q-card-section>

      <!-- Action Buttons -->
      <q-card-actions align="center" class="q-pa-md">
        <q-btn
          label="บันทึก"
          @click="submitSchedule"
          class="btn-confirm"
          :loading="isSubmitting || isLoadingSchedule"
          :disable="!isFormValid || isLoadingSchedule"
        />
      </q-card-actions>
    </q-card>

    <!-- Time Picker Dialog for Check-in -->
    <q-dialog v-model="showCheckInTimePicker" persistent>
      <q-time v-model="scheduleForm.checkInTime" mask="HH:mm" format24h class="time-picker">
        <div class="row items-center justify-end q-gutter-sm">
          <q-btn label="ยกเลิก" color="primary" flat @click="showCheckInTimePicker = false" />
          <q-btn label="ตกลง" color="primary" flat @click="showCheckInTimePicker = false" />
        </div>
      </q-time>
    </q-dialog>

    <!-- Time Picker Dialog for Check-out -->
    <q-dialog v-model="showCheckOutTimePicker" persistent>
      <q-time v-model="scheduleForm.checkOutTime" mask="HH:mm" format24h class="time-picker">
        <div class="row items-center justify-end q-gutter-sm">
          <q-btn label="ยกเลิก" color="primary" flat @click="showCheckOutTimePicker = false" />
          <q-btn label="ตกลง" color="primary" flat @click="showCheckOutTimePicker = false" />
        </div>
      </q-time>
    </q-dialog>
  </q-dialog>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { useQuasar } from 'quasar'
import { useWorkingScheduleStore } from 'src/stores/attendance/workingSchedule'
import { WorkingScheduleService } from 'src/services/workingScheduleService'
import type {
  WorkingScheduleFormData,
  ScheduleDialogProps,
  ScheduleDialogEmits,
} from 'src/types/workingSchedule'

// Define props for dialog control
const props = withDefaults(defineProps<ScheduleDialogProps>(), {
  modelValue: false,
  userId: null,
  mode: 'create',
})

// Define emits
const emit = defineEmits<ScheduleDialogEmits>()

// Quasar instance for notifications
const $q = useQuasar()

// Working schedule store
const workingScheduleStore = useWorkingScheduleStore()

// Dialog state
const isDialogOpen = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value),
})

// Enhanced schedule form interface
interface ScheduleData {
  checkInTime: string
  checkOutTime: string
  lateThresholdMinutes: number
  earlyThresholdMinutes: number
}

// Form data
const scheduleForm = ref<ScheduleData>({
  checkInTime: '09:00',
  checkOutTime: '17:00',
  lateThresholdMinutes: 5,
  earlyThresholdMinutes: 30,
})

// Loading states
const isSubmitting = ref(false)
const isLoadingSchedule = ref(false)

// Time picker states
const showCheckInTimePicker = ref(false)
const showCheckOutTimePicker = ref(false)

// Form validation
const isFormValid = computed(() => {
  const hasValidTimes =
    scheduleForm.value.checkInTime !== '' && scheduleForm.value.checkOutTime !== ''
  const hasValidTimeFormat =
    WorkingScheduleService.isValidTime(scheduleForm.value.checkInTime) &&
    WorkingScheduleService.isValidTime(scheduleForm.value.checkOutTime)
  const hasValidTimeOrder = WorkingScheduleService.isValidTimeOrder(
    scheduleForm.value.checkInTime,
    scheduleForm.value.checkOutTime,
  )

  return hasValidTimes && hasValidTimeFormat && hasValidTimeOrder
})

// Validation messages
const validationMessages = computed(() => {
  const messages: string[] = []

  if (!WorkingScheduleService.isValidTime(scheduleForm.value.checkInTime)) {
    messages.push('เวลาเข้างานไม่ถูกต้อง')
  }

  if (!WorkingScheduleService.isValidTime(scheduleForm.value.checkOutTime)) {
    messages.push('เวลาออกงานไม่ถูกต้อง')
  }

  if (
    !WorkingScheduleService.isValidTimeOrder(
      scheduleForm.value.checkInTime,
      scheduleForm.value.checkOutTime,
    )
  ) {
    messages.push('เวลาออกงานต้องมากกว่าเวลาเข้างาน')
  }

  return messages
})

// Methods
const closeDialog = () => {
  isDialogOpen.value = false
  resetForm()
  workingScheduleStore.clearError()
}

const resetForm = () => {
  scheduleForm.value = {
    checkInTime: '09:00',
    checkOutTime: '17:00',
    lateThresholdMinutes: 5,
    earlyThresholdMinutes: 30,
  }
}

// Load existing schedule data
const loadScheduleData = async () => {
  if (!props.userId) return

  isLoadingSchedule.value = true
  try {
    const existingSchedule = await workingScheduleStore.fetchUserSchedule(props.userId)

    if (existingSchedule) {
      scheduleForm.value = {
        checkInTime: WorkingScheduleService.formatTimeForDisplay(
          existingSchedule.standard_check_in_time,
        ),
        checkOutTime: WorkingScheduleService.formatTimeForDisplay(
          existingSchedule.standard_check_out_time,
        ),
        lateThresholdMinutes: existingSchedule.late_threshold_minutes,
        earlyThresholdMinutes: existingSchedule.early_threshold_minutes,
      }
    }
  } catch (error) {
    console.error('Error loading schedule data:', error)
    // Don't show error notification here, just use defaults
  } finally {
    isLoadingSchedule.value = false
  }
}

const openCheckInTimePicker = () => {
  showCheckInTimePicker.value = true
}

const openCheckOutTimePicker = () => {
  showCheckOutTimePicker.value = true
}

const formatTime = (time: string): string => {
  if (!time) return '00:00'
  return time
}

const getTimePeriod = (time: string): 'AM' | 'PM' => {
  if (!time) return 'AM'
  const timeParts = time.split(':').map(Number)
  const hours = timeParts[0]
  if (hours === undefined) return 'AM'
  return hours < 12 ? 'AM' : 'PM'
}

const submitSchedule = async () => {
  if (!isFormValid.value) {
    $q.notify({
      type: 'negative',
      message: validationMessages.value.join(', ') || 'กรุณากรอกข้อมูลให้ครบถ้วน',
      position: 'bottom',
    })
    return
  }

  if (!props.userId) {
    $q.notify({
      type: 'negative',
      message: 'ไม่พบข้อมูลผู้ใช้',
      position: 'bottom',
    })
    return
  }

  isSubmitting.value = true

  try {
    const scheduleData: WorkingScheduleFormData = {
      userId: props.userId,
      standard_check_in_time: scheduleForm.value.checkInTime,
      standard_check_out_time: scheduleForm.value.checkOutTime,
      late_threshold_minutes: scheduleForm.value.lateThresholdMinutes,
      early_threshold_minutes: scheduleForm.value.earlyThresholdMinutes,
    }

    await workingScheduleStore.saveSchedule(scheduleData)

    $q.notify({
      type: 'positive',
      message: 'บันทึกตารางเวลาทำงานเรียบร้อยแล้ว',
      position: 'bottom',
    })

    emit('success')
    closeDialog()
  } catch (error: unknown) {
    console.error('Error submitting schedule:', error)
    const errorMessage = error instanceof Error ? error.message : 'เกิดข้อผิดพลาดในการบันทึกข้อมูล'
    $q.notify({
      type: 'negative',
      message: errorMessage,
      position: 'bottom',
    })
    emit('error', errorMessage)
  } finally {
    isSubmitting.value = false
  }
}

// Watchers
watch(
  () => props.modelValue,
  async (newValue) => {
    if (newValue && props.userId) {
      await loadScheduleData()
    }
  },
)

watch(
  () => props.userId,
  async (newUserId) => {
    if (newUserId && props.modelValue) {
      await loadScheduleData()
    }
  },
)

// Watch for dialog close to reset form
watch(isDialogOpen, (newValue) => {
  if (!newValue) {
    resetForm()
    workingScheduleStore.clearError()
  }
})
</script>

<style scoped>
.container {
  background-color: #deecff;
  padding: 16px;
  border-bottom-left-radius: 10px;
  border-bottom-right-radius: 10px;
  border-top-left-radius: 0px;
  border-top-right-radius: 0px;
}

.container-header {
  background-color: #294888;
  padding: 10px 16px;
  border-top-left-radius: 10px;
  border-top-right-radius: 10px;
  height: 45px;
  display: flex;
  align-items: center;
}

.schedule-section {
  display: flex;
  flex-direction: row;
  gap: 24px;
  justify-content: space-between;
}

.time-section {
  flex: 1 1 0;
  display: flex;
  flex-direction: column;
}

.time-selector-container {
  display: flex;
  justify-content: center;
}

.time-display {
  background-color: #294888;
  border-radius: 10px;
  padding: 20px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  cursor: pointer;
  transition: all 0.3s ease;
  min-width: 200px;
}

.time-display:hover {
  background-color: #1e3a6f;
  transform: translateY(-2px);
}

.time-value {
  color: white;
  font-size: 2rem;
  font-weight: bold;
  flex: 1;
  text-align: center;
}

.time-period {
  display: flex;
  flex-direction: column;
  gap: 4px;
  margin-left: 16px;
}

.period-option {
  background-color: rgba(255, 255, 255, 0.2);
  color: rgba(255, 255, 255, 0.6);
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 0.8rem;
  text-align: center;
  transition: all 0.3s ease;
}

.period-option.active {
  background-color: white;
  color: #294888;
  font-weight: bold;
}

.btn-confirm {
  background-color: #36b54d;
  color: white;
  width: 120px;
  border-radius: 8px;
  font-weight: bold;
}

.btn-confirm:hover {
  background-color: #2d9a42;
}

.gap-container {
  margin-bottom: 0;
}

.shadow-2 {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.time-picker {
  background-color: white;
}
</style>
