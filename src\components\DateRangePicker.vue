<template>
  <q-card flat>
    <div class="row items-center justify-between media-container">
      <span class="q-mr-md">วันที่</span>
      <div class="row items-center q-gutter-xs">
        <q-input
          v-model="localDateFrom"
          borderless
          dense
          mask="##/##/####"
          class="input-container"
        />
        <q-icon name="event" class="cursor-pointer text-h5">
          <q-popup-proxy cover transition-show="scale" transition-hide="scale">
            <q-date v-model="localDateFrom" mask="DD/MM/YYYY" class="faded-other-months">
              <div class="row items-center justify-end">
                <q-btn v-close-popup label="OK" color="primary" flat />
              </div>
            </q-date>
          </q-popup-proxy>
        </q-icon>
      </div>

      <span class="q-mx-md">ถึง</span>

      <div class="row items-center q-gutter-xs">
        <q-input v-model="localDateTo" borderless dense mask="##/##/####" class="input-container" />
        <q-icon name="event" class="cursor-pointer text-h5">
          <q-popup-proxy cover transition-show="scale" transition-hide="scale">
            <q-date v-model="localDateTo" mask="DD/MM/YYYY" class="faded-other-months">
              <div class="row items-center justify-end">
                <q-btn v-close-popup label="OK" color="primary" flat />
              </div>
            </q-date>
          </q-popup-proxy>
        </q-icon>
      </div>
    </div>
  </q-card>
</template>

<script setup lang="ts">
import { defineProps, defineEmits, ref, watch } from 'vue'

const props = defineProps({
  modelValueFrom: String,
  modelValueTo: String,
})
const emit = defineEmits(['update:modelValueFrom', 'update:modelValueTo'])

const localDateFrom = ref(props.modelValueFrom)
const localDateTo = ref(props.modelValueTo)

watch(localDateFrom, (val) => emit('update:modelValueFrom', val))
watch(localDateTo, (val) => emit('update:modelValueTo', val))
</script>

<style scoped>
.q-card {
  background-color: #c3e7dd;
  padding-left: 10px;
  padding-right: 10px;
  border-radius: 5px;
  width: auto;
  min-width: 425px;
  max-width: 430px;
}

.input-container {
  border-radius: 5px;
  margin-right: 7px;
  width: 100%;
  max-width: 120px;
}

.input-container :deep(.q-field__native) {
  text-align: center;
}

/* Fade out dates from other months in QDate component */
.faded-other-months :deep(.q-date__calendar-item--out) {
  opacity: 0.3;
}

.faded-other-months :deep(.q-date__calendar-item--out .q-btn) {
  color: #999 !important;
}

.faded-other-months :deep(.q-date__calendar-item--out:hover .q-btn) {
  background-color: rgba(0, 0, 0, 0.05) !important;
}

@media (max-width: 490px) {
  .input-container {
    width: 50px;
  }

  .q-card {
    min-width: 310px;
  }

  .media-container {
    height: 40px;
  }
}
</style>
