import { defineStore } from 'pinia'
import { BranchService } from 'src/services/branch'
import type { Branch } from 'src/types/branch'

export const useBranchStore = defineStore('counter', {
  state: () => ({
    branchs: [] as Branch[],
    branch: {} as Branch
  }),

  getters: {
    getBranchs: (s) =>
      s.branchs
        .filter((branch) => branch.name && branch.name.trim() !== '') // กรอง name ที่ไม่ว่าง
        .map((branch) => ({ label: branch.name, value: String(branch.id) })) // แปลงข้อมูลให้เหลือเฉพาะ name และ id
  },

  actions: {
    async fetchAllBranch() {
      try {
        const data = await BranchService.getAll();
        if (data) {
          this.branchs = data;
        }
      } catch (error) {
        console.error('Error fetching data:', error);
      }
    },
    async fetchBranch(id: number) {
      try {
        const data = await BranchService.getOne(id);
        if (data) {
          return (this.branch = data)
        }
      } catch (error) {
        console.error('Error fetching data:', error);
      }
    }
  },
})

