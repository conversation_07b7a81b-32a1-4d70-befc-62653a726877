import { api } from 'src/boot/axios';
import type { PurchaseOrderItems } from 'src/types/purchaseOrderitems';

export class PurchaseOrderItemService {

  static path = 'purchaseorderitem'

  // async getAll(): Promise<PurchaseOrder[]> {
  //   const response = await axios.get(API_URL)
  //   return response.data
  // },

  // // ดึงข้อมูล Purchase Order ตาม ID
  // async getById(id: number): Promise<PurchaseOrder> {
  //   const response = await axios.get(`${API_URL}/${id}`)
  //   return response.data
  // },

  static async addProduct(orderItems: PurchaseOrderItems[], orderId: number) {
    console.log(orderItems)
    await api.post(`${this.path}/${orderId}`, orderItems)
  }
  static async updatePoItems(orderItems: PurchaseOrderItems[]) {
    await api.put(`${this.path}`, orderItems)
  }
  static async deletePoItems(selectedIds: number[]) {
    console.log(selectedIds)
    await api.delete(`${this.path}/${selectedIds.join(",")}`, { data: { ids: selectedIds } })
  }



  // สร้าง Purchase Order ใหม่
  // async create(order: PurchaseOrder): Promise<PurchaseOrder> {
  //   const response = await axios.post(API_URL, order)
  //   return response.data
  // },

  // // อัปเดต Purchase Order
  // async update(id: number, order: Partial<PurchaseOrder>): Promise<PurchaseOrder> {
  //   const response = await axios.put(`${API_URL}/${id}`, order)
  //   return response.data
  // },

  // // ลบ Purchase Order
  // async delete(id: number): Promise<void> {
  //   await axios.delete(`${API_URL}/${id}`)
  // }
}
