import { api } from 'src/boot/axios'

export const dashboardService = {
  // Inventory APIs
  getStockByBranch: () => api.get('/dashboard/inventory/by-branch'),
  getTopRemainingProducts: (limit = 10, order = 'desc') => 
    api.get(`/dashboard/inventory/top-remaining-products?limit=${limit}&order=${order}`),
  getValueByGroup: () => api.get('/dashboard/inventory/value-by-group'),

  // Goods Receipt APIs
  getGoodsReceiptVolumeByMonth: () => api.get('/dashboard/goods-receipt/volume-by-month'),
  getTopSuppliers: () => api.get('/dashboard/goods-receipt/top-suppliers'),
  getGoodsReceiptValueByMonth: () => api.get('/dashboard/goods-receipt/value-by-month'),

  // Transfer APIs
  getTransferVolumeByBranch: () => api.get('/dashboard/transfer/volume-by-branch'),
  getTopIssuedProducts: () => api.get('/dashboard/transfer/top-issued-products'),
  getTransferFlowMap: () => api.get('/dashboard/transfer/flow-map'),

  // Purchase Order APIs
  getPurchaseOrderSummaryByMonth: () => api.get('/dashboard/purchase-orders/summary-by-month'),
  getTopPurchaseProducts: (limit = 5) => 
    api.get(`/dashboard/purchase-orders/top-products?limit=${limit}`),
  getSupplierRatio: () => api.get('/dashboard/purchase-orders/supplier-ratio'),

  // Performance APIs
  getTopOnTimeSuppliers: () => api.get('/dashboard/performance/top-on-time-suppliers'),
  getSlowProducts: () => api.get('/dashboard/performance/slow-products'),
  getSupplierEfficiency: () => api.get('/dashboard/performance/supplier-efficiency')
}