<template>
  <BranchShipmentNavigation></BranchShipmentNavigation>
  <div class="container q-pa-md q-ma-md page-fade-in">
    <div class="row justify-left q-mb-sx">
      <q-checkbox size="xs" v-model="slide" label="ส่งสินค้าระหว่างสาขา" />
      <q-checkbox size="xs" v-model="slide" label="เตรียมรายการ" />
      <q-checkbox size="xs" v-model="slide" label="ดำเนินรายการ" />
      <q-checkbox size="xs" v-model="slide" label="รายการทั้งหมด" />
    </div>
    <div class="row wrap-container">

      <div class="col-7 search-container">
        <SearchComponent placeholder="ค้นหา" />
      </div>
      <div class="col-2 filter-button-container">
        <FilterComponent v-model="selectedFilter" :filterOptions="filterOptions" class="q-mb-md" />
      </div>
      <div class="col-3">
        <q-btn
          flat
          @click="openDialog"
          class="add-button q-mb-md"
          label="สร้างการร้องขอสินค้ารายการใหม่"
        />
      </div>
    </div>

    <div>
      <span class="text-subtitle1 text-weight-bold q-mb-sm">ส่งสินค้าระหว่างสาขา</span>
      <q-card flat class="custom-table">
        <q-table
          class="body-table"
          :rows="stoStore.stoProcess"
          :columns="columnsSTO"
          row-key="id"
          :pagination="pagination"
          :rows-per-page-options="[]"
          style="height: 100%; max-height: 700px"
        >
        </q-table>
      </q-card>
    </div>

    <div style="margin-top: 20px">
      <span class="text-subtitle1 text-weight-bold q-mb-sm">เตรียมรายการ</span>
      <q-card flat class="custom-table">
        <q-table
          class="body-table"
          :rows="store.stsPrepare"
          :columns="columns"
          row-key="id"
          :pagination="pagination"
          :rows-per-page-options="[]"
          @row-click="openDetailsDialog"
          style="height: 100%; max-height: 700px"
        >
        </q-table>
      </q-card>
    </div>

    <div style="margin-top: 20px">
      <span class="text-subtitle1 text-weight-bold q-mb-sm">กำลังดำเนินการ</span>
      <q-card flat class="custom-table">
        <q-table
          class="body-table"
          :rows="store.stsProcess"
          :columns="columns"
          row-key="id"
          :pagination="pagination"
          :rows-per-page-options="[]"
          style="height: 100%; max-height: 700px"
        >
        </q-table>
      </q-card>
    </div>
    <AddSTSDialog v-model="addSTSDialogOpen" :mode="modeSTS"></AddSTSDialog>
    <STSDetailDialog v-model="stsDetailsDialogOpen" :mode="modeSTSDetails"></STSDetailDialog>
  </div>
</template>
<script setup lang="ts">
import type { QTableColumn } from 'quasar'
import BranchShipmentNavigation from 'src/components/BranchShipmentNavigation.vue'
import AddSTSDialog from 'src/components/dialog/stocktransferslip/addSTSDialog.vue'
import STSDetailDialog from 'src/components/dialog/stocktransferslip/STSDetailDialog.vue'
import FilterComponent from 'src/components/filterComponent.vue'
import SearchComponent from 'src/components/searchComponent.vue'
import { useStockTransferOrderStore } from 'src/stores/orders/stocktransferorder'
import { useStockTransferSlipStore } from 'src/stores/orders/stocktransferslip'
import type { StockTransferOrder } from 'src/types/stockTransferOrder'
import { onMounted, ref } from 'vue'
const store = useStockTransferSlipStore()
const stoStore = useStockTransferOrderStore()
onMounted(async () => {
  await store.fetchSTSByStatus()
  await store.fetchSTSByStatus()
})
const columnsSTO = <QTableColumn[]>[
  {
    name: 'code',
    label: 'เลขที่',
    field: 'code',
    align: 'left' as const,
    sortable: true,
  },
  {
    name: 'request_date',
    label: 'วันที่',
    field: (row) => {
      const date = new Date(row.request_date)
      const year = date.getFullYear()
      const month = String(date.getMonth() + 1).padStart(2, '0') // เดือนต้อง +1 เพราะเริ่มจาก 0
      const day = String(date.getDate()).padStart(2, '0')
      return `${year}/${month}/${day}`
    },
    align: 'left' as const,
    sortable: true,
  },
  {
    name: 'source_branch',
    label: 'สาขาต้นทาง',
    field: (row) => (row.source_branch ? row.source_branch.name : ''),
    align: 'left' as const,
    sortable: true,
  },
  {
    name: 'destiantion_branch',
    label: 'สาขาปลายทาง',
    field: (row) => (row.destination_branch ? row.destination_branch.name : ''),
    align: 'left' as const,
    sortable: true,
  },
  {
    name: 'user',
    label: 'พนักงาน',
    field: (row) => (row.user ? row.user.name : ''),
    align: 'left' as const,
    sortable: true,
  },
  {
    name: 'actions',
    label: '',
    align: 'center' as const,
    field: 'actions',
    sortable: false,
  },
]
const columns = <QTableColumn[]>[
  {
    name: 'code',
    label: 'เลขที่',
    field: 'code',
    align: 'left' as const,
    sortable: true,
  },
  {
    name: 'transfer_date',
    label: 'วันที่',
    field: (row) => {
      const date = new Date(row.transfer_date)
      const year = date.getFullYear()
      const month = String(date.getMonth() + 1).padStart(2, '0') // เดือนต้อง +1 เพราะเริ่มจาก 0
      const day = String(date.getDate()).padStart(2, '0')
      return `${year}/${month}/${day}`
    },
    align: 'left' as const,
    sortable: true,
  },
  {
    name: 'source_branch',
    label: 'สาขาต้นทาง',
    field: (row) => (row.source_branch ? row.source_branch.name : ''),
    align: 'left' as const,
    sortable: true,
  },
  {
    name: 'destiantion_branch',
    label: 'สาขาปลายทาง',
    field: (row) => (row.destination_branch ? row.destination_branch.name : ''),
    align: 'left' as const,
    sortable: true,
  },
  {
    name: 'user',
    label: 'พนักงาน',
    field: (row) => (row.user ? row.user.name : ''),
    align: 'left' as const,
    sortable: true,
  },
  {
    name: 'transfer_status',
    label: 'สถานะโอน',
    field: 'transfer_status',
    align: 'left' as const,
    sortable: true,
  },
  {
    name: 'actions',
    label: '',
    align: 'center' as const,
    field: 'actions',
    sortable: false,
  },
]
const pagination = ref({
  rowsPerPage: 12,
})
const selectedFilter = ref<string>('')

const filterOptions = [
  { label: 'รหัสสั่งซื้อสินค้า', value: 'code' },
  { label: 'ชื่อบริษัทจำหน่าย', value: 'name' },
  { label: 'รหัสบริษัทจำหน่าย', value: 'supplier_number' },
]

const addSTSDialogOpen = ref(false)
const stsDetailsDialogOpen = ref(false)
const modeSTS = ref('add')
const modeSTSDetails = ref('edit')
const slide = ref('')

async function openDetailsDialog(_evt: Event, row: StockTransferOrder) {
  console.log('🚀 Clicked Row Object:', row)
  await store.fetchDetailsBySTS(row.id)
  await store.fetchStockTransferSlipById(row.id)
  stsDetailsDialogOpen.value = true
  modeSTSDetails.value = 'edit'
}
const openDialog = () => {
  addSTSDialogOpen.value = true
  modeSTS.value = 'add'
}
</script>
<style scoped>
.green-card {
  background-color: #439e62;
  /* สีเขียว */
  color: white;
}

.yellow-card {
  background-color: #ed9b53;
  /* สีเหลือง */
  color: white;
}

.red-card {
  background-color: #b53638;
  /* สีแดง */
  color: white;
}

.blue-card {
  /* สีแดง */
  background-color: #83a7d8;
  color: white;
}

:deep(.q-table thead tr) {
  background-color: #91d2c1;
}

.container {
  background-color: white;
  border-radius: 10px;
  height: 100%;
  max-height: 100%;
}

.add-button {
  background-color: #294888;
  color: white;
  border-radius: 5px;
  width: 100%;
  min-width: 141px;
  max-width: 300px;
  height: 40px;
}

.wrap-container {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
  align-items: stretch;
}

.search-container {
  flex: 1;
  min-width: 300px;
  max-width: 1000px;
}

.filter-button-container {
  display: flex;
  gap: 16px;
  align-items: center;
  justify-content: flex-start;
}

.body-table {
  background-color: #e1edea;
}

.custom-table {
  border-radius: 10px;
  overflow: hidden;
}

@media (max-width: 1200px) {
  .wrap-container {
    flex-direction: column;
    align-items: stretch;
  }

  .filter-button-container {
    flex-direction: row;
    width: 100%;
  }

  .text-right {
    text-align: left;
  }
}
</style>
