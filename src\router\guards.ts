import type { NavigationGuardNext, RouteLocationNormalized, Router } from 'vue-router'
import { useAuthStore } from 'src/stores/auth/authStore'

/**
 * Authentication guard for protected routes
 */
export const authGuard = async (
  to: RouteLocationNormalized,
  from: RouteLocationNormalized,
  next: NavigationGuardNext,
) => {
  const authStore = useAuthStore()

  // Initialize auth state if not already done
  if (!authStore.isAuthenticated && !authStore.token) {
    authStore.initializeAuth()
  }

  // If coming from login page, give a moment for auth state to settle
  if (from.path === '/login' && authStore.isAuthenticated) {
    console.log('🔄 Coming from login, allowing navigation without validation')
    next()
    return
  }

  // Check if route requires authentication
  const requiresAuth = to.meta.requiresAuth === true

  if (requiresAuth) {
    if (!authStore.isAuthenticated) {
      console.log('🔒 Route requires authentication, redirecting to login')
      next('/login')
      return
    }

    // Check if token is expired
    if (authStore.isTokenExpired) {
      console.log('⏰ Token expired, attempting refresh...')

      try {
        await authStore.refreshToken()
        console.log('✅ Token refreshed successfully')
        next()
      } catch {
        console.log('❌ Token refresh failed, redirecting to login')
        authStore.clearAuth()
        next('/login')
      }
      return
    }

    // Only validate token with server occasionally (not on every route change)
    // This reduces unnecessary API calls and improves performance
    const lastValidation = sessionStorage.getItem('last_token_validation')
    const now = Date.now()
    const validationInterval = 5 * 60 * 1000 // 5 minutes

    if (!lastValidation || now - parseInt(lastValidation) > validationInterval) {
      try {
        const isValid = await authStore.validateToken()
        if (!isValid) {
          console.log('❌ Token validation failed, redirecting to login')
          authStore.clearAuth()
          next('/login')
          return
        }
        sessionStorage.setItem('last_token_validation', now.toString())
      } catch {
        console.log('❌ Token validation error, redirecting to login')
        authStore.clearAuth()
        next('/login')
        return
      }
    }
  }

  // If user is authenticated and trying to access login page, redirect to home
  if (to.path === '/login' && authStore.isAuthenticated && !authStore.isTokenExpired) {
    console.log('✅ User already authenticated, redirecting to home')
    next('/home')
    return
  }

  next()
}

/**
 * Setup authentication guards
 */
export const setupAuthGuards = (router: Router) => {
  router.beforeEach(authGuard)

  // Setup token auto-refresh
  const authStore = useAuthStore()
  authStore.setupTokenRefresh()
}
