<template>
  <div class="container q-pa-md q-ma-md page-fade-in">
    <div class="row wrap-container">
      <div class="col-8 col-md-12 col-sm-12 col-xs-12 search-container">
        <searchComponent v-model="searchQuery" placeholder="ค้นหา" />
      </div>
      <div class="col-4 filter-button-container">
        <FilterComponent
          v-model="selectedFilter"
          :filterOptions="filterOptions"
          class="filter-component q-mb-md"
        />
        <q-btn flat class="add-button q-mb-md" label="เพิ่มบริษัท" @click="openDialog" />
      </div>
    </div>
    <q-card flat class="custom-table">
      <q-table
        flat
        class="body-table"
        :rows="filteredSuppliers"
        :columns="columns"
        row-key="id"
        :pagination="pagination"
        :rows-per-page-options="[]"
        style="height: 100%; max-height: 700px"
      >
        <template #body-cell-actions="props">
          <q-td class="q-gutter-x-sm" style="min-width: 100px">
            <q-btn
              icon="edit"
              padding="none"
              flat
              @click="openEditDialog(props.row)"
              style="color: #e19f62"
            />
            <q-btn
              icon="delete"
              padding="none"
              style="color: #b53638"
              flat
              @click="handleOpenDeleteDialog(props.row)"
            />
            <q-btn
              icon="info"
              padding="none"
              style="color: #294888"
              flat
              @click="openInfoDialog(props.row)"
            />
          </q-td>
        </template>
      </q-table>
    </q-card>
    <ConfirmDeleteSupplierDialog
      v-model="formDelete"
      :item="selectedRow"
      @confirm="handleConfirmDelete"
    />

    <addSupplierDialog></addSupplierDialog>
    <infoSupplierDialog></infoSupplierDialog>
  </div>
</template>

<script setup lang="ts">
import FilterComponent from 'src/components/filterComponent.vue'
import searchComponent from 'src/components/searchComponent.vue'
import addSupplierDialog from 'src/components/dialog/addSupplierDialog.vue'
import infoSupplierDialog from 'src/components/dialog/infoSupplierDialog.vue'
import { useSupplierDialogStore } from 'src/stores/dialogs/dialog-supplier'
import { useSupplierStore } from 'src/stores/inventory/supplier'
import { ref, computed, onMounted, watch } from 'vue'
import type { Supplier } from 'src/types/supplier'
import ConfirmDeleteSupplierDialog from 'src/components/dialog/confirmDeleteSupplierDialog.vue'

const supplierDialogStore = useSupplierDialogStore()
const store = useSupplierStore()
const selectedFilter = ref<string>('')
const searchQuery = ref('')
const type = ref('')
const dialogStore = useSupplierDialogStore()
const selectedRow = ref<Supplier>(JSON.parse(JSON.stringify(store.form)))
const formDelete = ref(false)

const openDialog = () => {
  supplierDialogStore.open()
  console.log('open dialog')
}
const openEditDialog = (row: Supplier) => {
  store.form = { ...row }
  dialogStore.open('edit')
}

const openInfoDialog = (row: Supplier) => {
  store.form = { ...row }
  dialogStore.openInfo('info')
}

function handleOpenDeleteDialog(row: Supplier) {
  selectedRow.value = { ...row }
  formDelete.value = true
}

// ยืนยันการลบ
const handleConfirmDelete = async (row: Supplier) => {
  try {
    await store.deleteSupplier(row.id)
  } catch (error) {
    console.error('Error removing product:', error)
  }
}

const pagination = ref({
  rowsPerPage: 12,
})
const filterOptions = [
  { label: 'รหัสบริษัทจำหน่าย', value: 'supplier_number' },
  { label: 'ชื่อบริษัทจำหน่าย', value: 'name' },
  { label: 'เบอร์โทร', value: 'tel' },
  { label: 'ผู้ติดต่อ', value: 'contact_name' },
]

// ฟิลเตอร์ผู้จัดจำหน่าย
const filteredSuppliers = computed(() => store.suppliers)

// ตรวจสอบเมื่อมีการเปลี่ยนแปลงใน searchQuery หรือ selectedFilter
watch([searchQuery, selectedFilter, type], async ([search, filter, type]) => {
  if (search.trim() || filter.trim()) {
    await store.filterSuppliers(search, filter, type)
  } else {
    await store.loadSuppliers()
  }
})

// เมื่อโหลดเพจเสร็จ
onMounted(async () => {
  try {
    await store.loadSuppliers()
  } catch (error) {
    console.error('Error fetching suppliers:', error)
  }
})

const columns = [
  {
    name: 'id',
    label: 'ลำดับ',
    field: 'id',
    align: 'left' as const,
    sortable: true,
  },
  {
    name: 'supplier_number',
    label: 'รหัสบริษัท',
    field: 'supplier_number',
    align: 'left' as const,
    sortable: true,
  },
  {
    name: 'name',
    label: 'ชื่อบริษัท',
    field: 'name',
    align: 'left' as const,
    sortable: true,
  },
  {
    name: 'tel',
    label: 'เบอร์โทร',
    field: 'tel',
    align: 'left' as const,
    sortable: true,
  },
  {
    name: 'contact_name',
    label: 'ผู้ติดต่อ',
    field: 'contact_name',
    align: 'left' as const,
    sortable: true,
  },

  {
    name: 'actions',
    label: '',
    align: 'center' as const,
    field: 'actions',
    sortable: false,
  },
]
</script>

<style scoped>
:deep(.q-table thead tr) {
  background-color: #91d2c1;
}

.container {
  background-color: white;
  border-radius: 10px;
  height: 100%;
  max-height: 800px;
}

.add-button {
  background-color: #294888;
  color: white;
  border-radius: 5px;
  width: 100%;
  min-width: 141px;
  max-width: 300px;
  height: 40px;
}

.wrap-container {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
  align-items: stretch;
}

.search-container {
  flex: 1;
  min-width: 311px;
  max-width: 1042px;
}

.filter-button-container {
  display: flex;
  gap: 16px;
  align-items: center;
  justify-content: flex-start;
}

@media (max-width: 1200px) {
  .wrap-container {
    flex-direction: column;
    align-items: stretch;
  }

  .filter-button-container {
    flex-direction: row;
    width: 100%;
  }

  .text-right {
    text-align: left;
  }
}
</style>
