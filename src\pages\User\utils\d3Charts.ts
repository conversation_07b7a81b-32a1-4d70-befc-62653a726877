import * as d3 from 'd3'

export interface WorkHoursDataItem {
  employeeId: number
  hours: number
  employeeName: string
}

export interface LateCheckInDataItem {
  employeeId: number
  lateDays: number
  employeeName: string
}

export interface GaugeDataItem {
  fullTime: number
  partTime: number
}

export const createWorkHoursChart = (chartContainer: HTMLDivElement, data: WorkHoursDataItem[]) => {
  // Clear previous chart
  d3.select(chartContainer).selectAll('*').remove()

  // Get container dimensions for responsive design
  const containerWidth = chartContainer.clientWidth || 600
  const containerHeight = 220

  // Set dimensions and margins
  const margin = { top: 20, right: 30, bottom: 60, left: 60 }
  const width = containerWidth - margin.left - margin.right
  const height = containerHeight - margin.bottom - margin.top

  // Create SVG with responsive viewBox
  const svg = d3
    .select(chartContainer)
    .append('svg')
    .attr('width', '100%')
    .attr('height', containerHeight)
    .attr('viewBox', `0 0 ${containerWidth} ${containerHeight}`)
    .attr('preserveAspectRatio', 'xMidYMid meet')

  const g = svg.append('g').attr('transform', `translate(${margin.left},${margin.top})`)

  // Calculate dynamic max value based on data
  const maxHours = data.length > 0 ? Math.max(...data.map((item) => item.hours)) : 50
  const chartMax = Math.ceil(maxHours * 1.2) // Add 20% padding

  // Set scales
  const xScale = d3
    .scaleBand()
    .domain(data.map((d) => `ID: ${d.employeeId}`))
    .range([0, width])
    .padding(0.7)

  const yScale = d3.scaleLinear().domain([0, chartMax]).range([height, 0])

  const tooltip = d3
    .select('body')
    .append('div')
    .attr('class', 'd3-tooltip')
    .style('position', 'absolute')
    .style('visibility', 'hidden')
    .style('background-color', 'rgba(0, 0, 0, 0.8)')
    .style('color', '#ffffff')
    .style('padding', '10px')
    .style('border-radius', '6px')
    .style('border', '1px solid #26A69A')
    .style('font-size', '12px')
    .style('z-index', '1000')

  // Create bars with rounded top corners only - only show bars with value > 0
  const bars = g
    .selectAll('.bar')
    .data(data.filter((d) => d.hours > 0))
    .enter()
    .append('path')
    .attr('class', 'bar')
    .attr('fill', '#26A69A')
    .attr('stroke', '#1E8E7E')
    .attr('stroke-width', 1)
    .style('cursor', 'pointer')
    .style('opacity', 0)

  // Function to create bar path
  const createBarPath = (d: WorkHoursDataItem, animationHeight: number) => {
    const x = xScale(`ID: ${d.employeeId}`) || 0
    const width = xScale.bandwidth()
    const y = yScale(animationHeight)
    const barHeight = height - yScale(animationHeight)
    const radius = 12

    // Create path with rounded top corners only
    if (barHeight <= radius) {
      // If bar is too short, just create a rectangle
      return `M${x},${height} L${x},${y} L${x + width},${y} L${x + width},${height} Z`
    } else {
      // Create rounded top corners
      return `
        M${x},${height}
        L${x},${y + radius}
        Q${x},${y} ${x + radius},${y}
        L${x + width - radius},${y}
        Q${x + width},${y} ${x + width},${y + radius}
        L${x + width},${height}
        Z
      `
    }
  }

  // Initial state: bars start from bottom (height = 0)
  bars.attr('d', (d) => createBarPath(d, 0))

  // Animate bars growing from bottom to full height
  bars
    .transition()
    .duration(800)
    .delay((d, i) => i * 150) // Stagger animation
    .ease(d3.easeQuadOut)
    .style('opacity', 1)
    .attrTween('d', function (d) {
      const interpolate = d3.interpolate(0, d.hours)
      return function (t) {
        return createBarPath(d, interpolate(t))
      }
    })
  // Add event handlers after animation
  bars
    .on('mouseover', function (event, d) {
      d3.select(this).attr('fill', '#1E8E7E')
      tooltip.style('visibility', 'visible').html(
        `<div><strong>${d.employeeName}</strong></div>
           <div>Employee ID: ${d.employeeId}</div>
           <div>Working Hours: ${d.hours} hours</div>`,
      )
    })
    .on('mousemove', function (event) {
      tooltip.style('top', event.pageY - 10 + 'px').style('left', event.pageX + 10 + 'px')
    })
    .on('mouseout', function () {
      d3.select(this).attr('fill', '#26A69A')
      tooltip.style('visibility', 'hidden')
    })

  // Add X axis
  g.append('g')
    .attr('transform', `translate(0,${height})`)
    .call(d3.axisBottom(xScale))
    .selectAll('text')
    .style('text-anchor', 'middle')
    .style('font-size', '11px')
    .style('fill', '#555')

  // Style X axis line
  g.select('.domain').style('stroke', '#bbb').style('stroke-width', '1px')

  // Add Y axis
  g.append('g')
    .call(d3.axisLeft(yScale).ticks(Math.min(5, Math.max(3, Math.ceil(chartMax / 10)))))
    .selectAll('text')
    .style('font-size', '11px')
    .style('fill', '#555')

  // Style Y axis line and tick lines
  g.selectAll('.domain').style('stroke', '#bbb').style('stroke-width', '1px')

  g.selectAll('.tick line').style('stroke', '#e5e5e5').style('stroke-width', '1px')

  // Add Y axis label
  g.append('text')
    .attr('transform', 'rotate(-90)')
    .attr('y', 0 - margin.left)
    .attr('x', 0 - height / 2)
    .attr('dy', '1em')
    .style('text-anchor', 'middle')
    .style('font-size', '12px')
    .style('fill', '#555')
    .text('Total Working Hours')

  // Add X axis label
  g.append('text')
    .attr('transform', `translate(${width / 2}, ${height + margin.bottom - 10})`)
    .style('text-anchor', 'middle')
    .style('font-size', '12px')
    .style('fill', '#555')
    .text('Employee ID')

  // Add legend (centered Working Hours label)
  const legend = svg
    .append('g')
    .attr('transform', `translate(${containerWidth / 2}, ${margin.top - 5})`)

  legend
    .append('rect')
    .attr('x', -62)
    .attr('y', -15)
    .attr('width', 12)
    .attr('height', 12)
    .attr('fill', '#26A69A')
    .attr('rx', 2)

  legend
    .append('text')
    .attr('x', 0)
    .attr('y', -5)
    .style('font-size', '12px')
    .style('fill', '#555')
    .style('text-anchor', 'middle')
    .text('Working Hours')
}

export const createLateCheckInChart = (
  chartContainer: HTMLDivElement,
  data: LateCheckInDataItem[],
) => {
  // Clear previous chart
  d3.select(chartContainer).selectAll('*').remove()

  // Get container dimensions for responsive design
  const containerWidth = chartContainer.clientWidth || 600
  const containerHeight = 220

  // Set dimensions and margins
  const margin = { top: 20, right: 30, bottom: 60, left: 60 }
  const width = containerWidth - margin.left - margin.right
  const height = containerHeight - margin.bottom - margin.top

  // Create SVG with responsive viewBox
  const svg = d3
    .select(chartContainer)
    .append('svg')
    .attr('width', '100%')
    .attr('height', containerHeight)
    .attr('viewBox', `0 0 ${containerWidth} ${containerHeight}`)
    .attr('preserveAspectRatio', 'xMidYMid meet')

  const g = svg.append('g').attr('transform', `translate(${margin.left},${margin.top})`)

  // Calculate dynamic max value based on data
  const maxLateDays = data.length > 0 ? Math.max(...data.map((item) => item.lateDays)) : 8
  const chartMax = Math.ceil(maxLateDays * 1.2) // Add 20% padding

  // Set scales
  const xScale = d3
    .scaleBand()
    .domain(data.map((d) => `ID: ${d.employeeId}`))
    .range([0, width])
    .padding(0.7)

  const yScale = d3.scaleLinear().domain([0, chartMax]).range([height, 0])

  const tooltip = d3
    .select('body')
    .append('div')
    .attr('class', 'd3-tooltip')
    .style('position', 'absolute')
    .style('visibility', 'hidden')
    .style('background-color', 'rgba(0, 0, 0, 0.8)')
    .style('color', '#ffffff')
    .style('padding', '10px')
    .style('border-radius', '6px')
    .style('border', '1px solid #AB47BC')
    .style('font-size', '12px')
    .style('z-index', '1000')

  // Create bars with rounded top corners only - only show bars with value > 0
  const bars = g
    .selectAll('.bar')
    .data(data.filter((d) => d.lateDays > 0))
    .enter()
    .append('path')
    .attr('class', 'bar')
    .attr('fill', '#AB47BC')
    .attr('stroke', '#9C27B0')
    .attr('stroke-width', 1)
    .style('cursor', 'pointer')
    .style('opacity', 0)

  // Function to create bar path
  const createBarPath = (d: LateCheckInDataItem, animationValue: number) => {
    const x = xScale(`ID: ${d.employeeId}`) || 0
    const width = xScale.bandwidth()
    const y = yScale(animationValue)
    const barHeight = height - yScale(animationValue)
    const radius = 12

    // Create path with rounded top corners only
    if (barHeight <= radius) {
      // If bar is too short, just create a rectangle
      return `M${x},${height} L${x},${y} L${x + width},${y} L${x + width},${height} Z`
    } else {
      // Create rounded top corners
      return `
        M${x},${height}
        L${x},${y + radius}
        Q${x},${y} ${x + radius},${y}
        L${x + width - radius},${y}
        Q${x + width},${y} ${x + width},${y + radius}
        L${x + width},${height}
        Z
      `
    }
  }

  // Initial state: bars start from bottom (value = 0)
  bars.attr('d', (d) => createBarPath(d, 0))

  // Animate bars growing from bottom to full height
  bars
    .transition()
    .duration(800)
    .delay((d, i) => i * 150) // Stagger animation
    .ease(d3.easeQuadOut)
    .style('opacity', 1)
    .attrTween('d', function (d) {
      const interpolate = d3.interpolate(0, d.lateDays)
      return function (t) {
        return createBarPath(d, interpolate(t))
      }
    })

  // Add event handlers after animation
  bars
    .on('mouseover', function (event, d) {
      d3.select(this).attr('fill', '#9C27B0')
      tooltip.style('visibility', 'visible').html(
        `<div><strong>${d.employeeName}</strong></div>
           <div>Employee ID: ${d.employeeId}</div>
           <div>Late Days: ${d.lateDays} days</div>`,
      )
    })
    .on('mousemove', function (event) {
      tooltip.style('top', event.pageY - 10 + 'px').style('left', event.pageX + 10 + 'px')
    })
    .on('mouseout', function () {
      d3.select(this).attr('fill', '#AB47BC')
      tooltip.style('visibility', 'hidden')
    })

  // Add X axis
  g.append('g')
    .attr('transform', `translate(0,${height})`)
    .call(d3.axisBottom(xScale))
    .selectAll('text')
    .style('text-anchor', 'middle')
    .style('font-size', '11px')
    .style('fill', '#555')

  // Style X axis line
  g.select('.domain').style('stroke', '#bbb').style('stroke-width', '1px')

  // Add Y axis
  g.append('g')
    .call(d3.axisLeft(yScale).ticks(Math.min(5, Math.max(3, Math.ceil(chartMax / 10)))))
    .selectAll('text')
    .style('font-size', '11px')
    .style('fill', '#555')

  // Style Y axis line and tick lines
  g.selectAll('.domain').style('stroke', '#bbb').style('stroke-width', '1px')

  g.selectAll('.tick line').style('stroke', '#e5e5e5').style('stroke-width', '1px')

  // Add Y axis label
  g.append('text')
    .attr('transform', 'rotate(-90)')
    .attr('y', 0 - margin.left)
    .attr('x', 0 - height / 2)
    .attr('dy', '1em')
    .style('text-anchor', 'middle')
    .style('font-size', '12px')
    .style('fill', '#555')
    .text('Number of Late Days')

  // Add X axis label
  g.append('text')
    .attr('transform', `translate(${width / 2}, ${height + margin.bottom - 10})`)
    .style('text-anchor', 'middle')
    .style('font-size', '12px')
    .style('fill', '#555')
    .text('Employee ID')

  // Add legend (centered Late Days label)
  const legend = svg
    .append('g')
    .attr('transform', `translate(${containerWidth / 2}, ${margin.top - 5})`)

  legend
    .append('rect')
    .attr('x', -50)
    .attr('y', -15)
    .attr('width', 12)
    .attr('height', 12)
    .attr('fill', '#AB47BC')
    .attr('rx', 2)

  legend
    .append('text')
    .attr('x', 0)
    .attr('y', -5)
    .style('font-size', '12px')
    .style('fill', '#555')
    .style('text-anchor', 'middle')
    .text('Late Days')
}

// Utility function to clean up D3 tooltips
export const cleanupD3Tooltips = () => {
  d3.selectAll('.d3-tooltip').remove()
}

export interface DoughnutDataItem {
  label: string
  value: number
  color: string
}

export const createDoughnutChart = (
  chartContainer: HTMLElement,
  data: DoughnutDataItem[],
  options?: { cutout?: number },
) => {
  // Clear previous chart
  d3.select(chartContainer).selectAll('*').remove()

  // Get container dimensions
  const containerWidth = chartContainer.clientWidth || 120
  const containerHeight = chartContainer.clientHeight || 120

  // Create SVG
  const svg = d3
    .select(chartContainer)
    .append('svg')
    .attr('width', '100%')
    .attr('height', '100%')
    .attr('viewBox', `0 0 ${containerWidth} ${containerHeight}`)
    .attr('preserveAspectRatio', 'xMidYMid meet')

  const width = containerWidth
  const height = containerHeight
  const radius = Math.min(width, height) / 2 - 5

  const g = svg.append('g').attr('transform', `translate(${width / 2}, ${height / 2})`)

  // Calculate total for percentages
  const total = data.reduce((sum, item) => sum + item.value, 0)

  // Create pie generator
  const pie = d3
    .pie<DoughnutDataItem>()
    .value((d) => d.value)
    .sort(null)

  // Get cutout value for animation
  const cutout = options?.cutout || 0.7

  // Create arc generator with configurable cutout
  const arc = d3
    .arc<d3.PieArcDatum<DoughnutDataItem>>()
    .innerRadius(radius * cutout)
    .outerRadius(radius)

  // Create tooltip
  const tooltip = d3
    .select('body')
    .append('div')
    .attr('class', 'd3-tooltip')
    .style('position', 'absolute')
    .style('visibility', 'hidden')
    .style('background-color', 'rgba(0, 0, 0, 0.8)')
    .style('color', '#ffffff')
    .style('padding', '10px')
    .style('border-radius', '6px')
    .style('font-size', '12px')
    .style('z-index', '1000')

  // Create arcs
  const arcs = g.selectAll('.arc').data(pie(data)).enter().append('g').attr('class', 'arc')

  const paths = arcs
    .append('path')
    .attr('fill', (d) => d.data.color)
    .style('cursor', 'pointer')
    .style('opacity', 0)

  // Animate arcs with smooth angle rotation only
  paths
    .transition()
    .duration(1200)
    .delay((d, i) => i * 300)
    .ease(d3.easeCubicOut)
    .style('opacity', 1)
    .attrTween('d', function (d) {
      // Interpolate from startAngle to endAngle only
      const interpolateAngle = d3.interpolate(d.startAngle, d.endAngle)

      return function (t) {
        const currentEndAngle = interpolateAngle(t)

        const arcData = {
          ...d,
          startAngle: d.startAngle,
          endAngle: currentEndAngle,
        } as d3.PieArcDatum<DoughnutDataItem>

        return arc(arcData) || ''
      }
    })

  // Add event handlers
  paths
    .on('mouseover', function (event, d) {
      const percentage = total > 0 ? Math.round((d.data.value / total) * 100) : 0
      tooltip.style('visibility', 'visible').html(
        `<div><strong>${d.data.label}</strong></div>
         <div>${d.data.value} (${percentage}%)</div>`,
      )
      d3.select(this).style('opacity', 0.8)
    })
    .on('mousemove', function (event) {
      tooltip.style('top', event.pageY - 10 + 'px').style('left', event.pageX + 10 + 'px')
    })
    .on('mouseout', function () {
      tooltip.style('visibility', 'hidden')
      d3.select(this).style('opacity', 1)
    })
}

export const createGaugeChart = (chartContainer: HTMLDivElement, data: GaugeDataItem) => {
  // Clear previous chart
  d3.select(chartContainer).selectAll('*').remove()

  // Get container dimensions
  const containerWidth = chartContainer.clientWidth || 300
  const containerHeight = 180

  // Create SVG
  const svg = d3
    .select(chartContainer)
    .append('svg')
    .attr('width', '100%')
    .attr('height', containerHeight)
    .attr('viewBox', `0 0 ${containerWidth} ${containerHeight}`)
    .attr('preserveAspectRatio', 'xMidYMid meet')

  const width = containerWidth
  const height = containerHeight
  const radius = Math.min(width, height * 2) / 2 - 10

  const g = svg.append('g').attr('transform', `translate(${width / 2}, ${height - 10})`)

  // Data preparation
  const total = data.fullTime + data.partTime
  const pieData = [
    { label: 'พนักงานประจำ', value: data.fullTime, color: '#26A69A' },
    { label: 'พนักงานพาร์ทไทม์', value: data.partTime, color: '#AB47BC' },
  ]

  type PieDataItem = {
    label: string
    value: number
    color: string
  }

  // Create pie generator for semicircle
  const pie = d3
    .pie<PieDataItem>()
    .value((d) => d.value)
    .startAngle(-Math.PI / 2)
    .endAngle(Math.PI / 2)
    .sort(null)

  // Create arc generator
  const arc = d3
    .arc<d3.PieArcDatum<PieDataItem>>()
    .innerRadius(radius * 0.75)
    .outerRadius(radius)

  // Create tooltip
  const tooltip = d3
    .select('body')
    .append('div')
    .attr('class', 'd3-tooltip')
    .style('position', 'absolute')
    .style('visibility', 'hidden')
    .style('background-color', 'rgba(0, 0, 0, 0.8)')
    .style('color', '#ffffff')
    .style('padding', '10px')
    .style('border-radius', '6px')
    .style('font-size', '12px')
    .style('z-index', '1000')

  // Create arcs
  const arcs = g.selectAll('.arc').data(pie(pieData)).enter().append('g').attr('class', 'arc')

  const paths = arcs
    .append('path')
    .attr('fill', (d) => d.data.color)
    .style('cursor', 'pointer')
    .style('opacity', 0)

  // Animate gauge arcs from left to right
  paths
    .transition()
    .duration(1200)
    .delay((d, i) => i * 300)
    .ease(d3.easeQuadOut)
    .style('opacity', 1)
    .attrTween('d', function (d) {
      const interpolate = d3.interpolate(d.startAngle, d.endAngle)
      return function (t) {
        const currentEndAngle = d.startAngle + (interpolate(1) - d.startAngle) * t
        const arcData = {
          ...d,
          endAngle: currentEndAngle,
        } as d3.PieArcDatum<PieDataItem>
        return arc(arcData) || ''
      }
    })

  // Add event handlers
  paths
    .on('mouseover', function (event, d) {
      const percentage = total > 0 ? Math.round((d.data.value / total) * 100) : 0
      tooltip.style('visibility', 'visible').html(
        `<div><strong>${d.data.label}</strong></div>
         <div>${d.data.value} คน (${percentage}%)</div>`,
      )
      d3.select(this).style('opacity', 0.8)
    })
    .on('mousemove', function (event) {
      tooltip.style('top', event.pageY - 10 + 'px').style('left', event.pageX + 10 + 'px')
    })
    .on('mouseout', function () {
      tooltip.style('visibility', 'hidden')
      d3.select(this).style('opacity', 1)
    })
}
